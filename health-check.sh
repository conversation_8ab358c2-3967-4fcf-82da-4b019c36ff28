#!/bin/bash

# Memorial 项目健康检查脚本
# 检查所有服务的运行状态

set -e

# 配置
BACKEND_PORT=8008
FRONTEND_PORT=4001
TIMEOUT=10

# 颜色输出
info() { echo -e "\033[1;34m[INFO]\033[0m $1"; }
success() { echo -e "\033[1;32m[✓]\033[0m $1"; }
warning() { echo -e "\033[1;33m[⚠]\033[0m $1"; }
error() { echo -e "\033[1;31m[✗]\033[0m $1"; }

# 检查服务函数
check_service() {
    local name=$1
    local url=$2
    local expected_status=${3:-200}
    
    info "检查 $name..."
    
    if response=$(curl -s -w "%{http_code}" -o /dev/null --connect-timeout $TIMEOUT "$url" 2>/dev/null); then
        if [[ "$response" == "$expected_status" ]]; then
            success "$name 运行正常 (HTTP $response)"
            return 0
        else
            warning "$name 响应异常 (HTTP $response)"
            return 1
        fi
    else
        error "$name 无法访问"
        return 1
    fi
}

# 检查端口占用
check_port() {
    local port=$1
    local name=$2
    
    if lsof -i :$port &> /dev/null; then
        success "$name 端口 $port 已占用"
        return 0
    else
        error "$name 端口 $port 未占用"
        return 1
    fi
}

# 检查数据库连接
check_database() {
    info "检查数据库连接..."
    
    # 从环境变量或默认值获取数据库配置
    DB_HOST=${POSTGRES_SERVER:-localhost}
    DB_PORT=${POSTGRES_PORT:-5432}
    DB_USER=${POSTGRES_USER:-memorial}
    DB_NAME=${POSTGRES_DB:-memorial}
    
    if command -v psql &> /dev/null; then
        if PGPASSWORD=${POSTGRES_PASSWORD:-memorial} psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1;" &> /dev/null; then
            success "数据库连接正常"
            return 0
        else
            error "数据库连接失败"
            return 1
        fi
    else
        warning "未安装 psql，跳过数据库检查"
        return 0
    fi
}

# 检查依赖
check_dependencies() {
    info "检查系统依赖..."
    
    local deps_ok=true
    
    # 检查 Python
    if command -v python3 &> /dev/null; then
        local python_version=$(python3 --version | cut -d' ' -f2)
        success "Python $python_version"
    else
        error "Python3 未安装"
        deps_ok=false
    fi
    
    # 检查 Node.js
    if command -v node &> /dev/null; then
        local node_version=$(node --version)
        success "Node.js $node_version"
    else
        error "Node.js 未安装"
        deps_ok=false
    fi
    
    # 检查 npm
    if command -v npm &> /dev/null; then
        local npm_version=$(npm --version)
        success "npm $npm_version"
    else
        error "npm 未安装"
        deps_ok=false
    fi
    
    if [[ "$deps_ok" == "true" ]]; then
        success "系统依赖检查通过"
        return 0
    else
        error "系统依赖检查失败"
        return 1
    fi
}

# 检查环境配置
check_environment() {
    info "检查环境配置..."
    
    # 检查 .env 文件
    if [[ -f ".env" ]]; then
        success "找到 .env 配置文件"
        
        # 检查关键环境变量
        source .env 2>/dev/null || true
        
        local env_ok=true
        
        if [[ -z "${SECRET_KEY:-}" ]]; then
            warning "SECRET_KEY 未设置"
            env_ok=false
        fi
        
        if [[ -z "${DATABASE_URL:-}" ]]; then
            warning "DATABASE_URL 未设置"
            env_ok=false
        fi
        
        if [[ "$env_ok" == "true" ]]; then
            success "环境变量配置正常"
        else
            warning "部分环境变量未配置"
        fi
    else
        warning "未找到 .env 文件，建议从 .env.example 复制"
    fi
}

# 主检查函数
main() {
    echo "🔍 Memorial 项目健康检查"
    echo "=========================="
    echo ""
    
    local overall_status=0
    
    # 检查系统依赖
    check_dependencies || overall_status=1
    echo ""
    
    # 检查环境配置
    check_environment
    echo ""
    
    # 检查数据库
    check_database || overall_status=1
    echo ""
    
    # 检查端口占用
    info "检查端口占用..."
    check_port $BACKEND_PORT "后端服务" || overall_status=1
    check_port $FRONTEND_PORT "前端服务" || overall_status=1
    echo ""
    
    # 检查服务健康状态
    info "检查服务健康状态..."
    check_service "后端 API" "http://localhost:$BACKEND_PORT/health" || overall_status=1
    check_service "前端应用" "http://localhost:$FRONTEND_PORT" || overall_status=1
    
    # 检查 API 端点
    check_service "API 文档" "http://localhost:$BACKEND_PORT/docs" || overall_status=1
    check_service "API v1" "http://localhost:$BACKEND_PORT/api/v1/healthcheck" || overall_status=1
    
    echo ""
    echo "=========================="
    
    if [[ $overall_status -eq 0 ]]; then
        success "🎉 所有检查通过，系统运行正常！"
        echo ""
        echo "📍 服务地址:"
        echo "  🌐 前端应用: http://localhost:$FRONTEND_PORT"
        echo "  🔧 后端 API: http://localhost:$BACKEND_PORT"
        echo "  📚 API 文档: http://localhost:$BACKEND_PORT/docs"
    else
        error "❌ 发现问题，请检查上述错误信息"
        echo ""
        echo "💡 常见解决方案:"
        echo "  1. 确保服务已启动: ./start_memorial.sh"
        echo "  2. 检查端口占用: lsof -i :$BACKEND_PORT -i :$FRONTEND_PORT"
        echo "  3. 查看服务日志: docker logs <container_name>"
        echo "  4. 重启服务: ./stop_server.sh && ./start_memorial.sh"
    fi
    
    exit $overall_status
}

# 执行主函数
main "$@"
