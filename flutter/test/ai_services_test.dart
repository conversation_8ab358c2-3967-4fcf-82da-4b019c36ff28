import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:http/http.dart' as http;
import 'package:provider/provider.dart';

import 'package:memorial/core/services/ai_service.dart';
import 'package:memorial/core/providers/ai_provider.dart';
import 'package:memorial/features/memorial/ai_services_screen.dart';
import 'package:memorial/features/memorial/ai_photo_repair_screen.dart';
import 'package:memorial/features/memorial/ai_voice_cloning_screen.dart';
import 'package:memorial/features/memorial/ai_task_list_screen.dart';

// 生成Mock类
@GenerateMocks([http.Client, AIService])
import 'ai_services_test.mocks.dart';

void main() {
  group('AI Services Tests', () {
    late MockClient mockClient;
    late MockAIService mockAIService;
    late AIProvider aiProvider;

    setUp(() {
      mockClient = MockClient();
      mockAIService = MockAIService();
      aiProvider = AIProvider(mockAIService);
    });

    group('AIService Tests', () {
      test('should check health successfully', () async {
        // Arrange
        const responseBody = '''
        {
          "status": "healthy",
          "services": {
            "replicate": {"status": "healthy"},
            "task_manager": {"status": "healthy"}
          },
          "timestamp": **********
        }
        ''';

        when(mockClient.get(
          Uri.parse('http://localhost:8000/api/v1/ai-enhanced/health'),
          headers: anyNamed('headers'),
        )).thenAnswer((_) async => http.Response(responseBody, 200));

        final aiService = AIService(client: mockClient);

        // Act
        final result = await aiService.checkHealth();

        // Assert
        expect(result.status, 'healthy');
        expect(result.isHealthy, true);
        expect(result.services, isNotEmpty);
      });

      test('should get available models successfully', () async {
        // Arrange
        const responseBody = '''
        {
          "success": true,
          "models": {
            "photo_restoration": {
              "name": "Real-ESRGAN",
              "capabilities": ["去噪", "修复破损"]
            }
          },
          "statistics": {"total_models": 1},
          "features": {"batch_processing": true}
        }
        ''';

        when(mockClient.get(
          Uri.parse('http://localhost:8000/api/v1/ai-enhanced/models'),
          headers: anyNamed('headers'),
        )).thenAnswer((_) async => http.Response(responseBody, 200));

        final aiService = AIService(client: mockClient);

        // Act
        final result = await aiService.getAvailableModels();

        // Assert
        expect(result.success, true);
        expect(result.models, isNotEmpty);
        expect(result.features['batch_processing'], true);
      });

      test('should handle API errors gracefully', () async {
        // Arrange
        when(mockClient.get(
          any,
          headers: anyNamed('headers'),
        )).thenAnswer((_) async => http.Response('Server Error', 500));

        final aiService = AIService(client: mockClient);

        // Act & Assert
        expect(
          () => aiService.checkHealth(),
          throwsA(isA<AIServiceException>()),
        );
      });

      test('should validate voice sample', () async {
        // Arrange
        const responseBody = '''
        {
          "success": true,
          "validation": {
            "is_valid": true,
            "duration": 10.5,
            "warnings": [],
            "recommendations": ["建议使用更高质量的录音"]
          },
          "language": "zh",
          "supported_languages": {}
        }
        ''';

        when(mockClient.send(any))
            .thenAnswer((_) async => http.StreamedResponse(
                  Stream.fromIterable([responseBody.codeUnits]),
                  200,
                ));

        final aiService = AIService(client: mockClient);
        final testFile = File('test_audio.wav');

        // Act
        final result = await aiService.validateVoiceSample(
          voiceFile: testFile,
          language: 'zh',
        );

        // Assert
        expect(result.success, true);
        expect(result.isValid, true);
        expect(result.duration, 10.5);
      });
    });

    group('AIProvider Tests', () {
      test('should initialize successfully', () async {
        // Arrange
        when(mockAIService.checkHealth()).thenAnswer((_) async => AIHealthStatus(
              status: 'healthy',
              services: {},
            ));
        when(mockAIService.getAvailableModels()).thenAnswer((_) async =>
            AIModelsResponse(
              success: true,
              models: {},
              statistics: {},
              features: {},
            ));
        when(mockAIService.getSupportedLanguages())
            .thenAnswer((_) async => {'languages': {}});
        when(mockAIService.getUserTasks()).thenAnswer((_) async => []);

        // Act
        await aiProvider.initialize();

        // Assert
        expect(aiProvider.healthStatus?.isHealthy, true);
        expect(aiProvider.modelsResponse?.success, true);
        expect(aiProvider.error, isNull);
      });

      test('should handle batch photo processing', () async {
        // Arrange
        final testFiles = [File('test1.jpg'), File('test2.jpg')];
        const taskId = 'test_task_123';

        when(mockAIService.batchProcessPhotos(
          files: anyNamed('files'),
          operation: anyNamed('operation'),
          scale: anyNamed('scale'),
          faceEnhance: anyNamed('faceEnhance'),
        )).thenAnswer((_) async => AITaskResponse(
              success: true,
              message: '任务创建成功',
              taskId: taskId,
            ));

        // Act
        final result = await aiProvider.batchProcessPhotos(
          files: testFiles,
          operation: 'restore',
        );

        // Assert
        expect(result, taskId);
        expect(aiProvider.error, isNull);
      });

      test('should handle voice cloning', () async {
        // Arrange
        final testFile = File('test_voice.wav');
        const taskId = 'voice_task_123';

        when(mockAIService.cloneVoiceEnhanced(
          voiceFile: anyNamed('voiceFile'),
          text: anyNamed('text'),
          language: anyNamed('language'),
          enhanceQuality: anyNamed('enhanceQuality'),
          speed: anyNamed('speed'),
          pitchShift: anyNamed('pitchShift'),
        )).thenAnswer((_) async => AITaskResponse(
              success: true,
              message: '语音克隆任务创建成功',
              taskId: taskId,
            ));

        // Act
        final result = await aiProvider.cloneVoice(
          voiceFile: testFile,
          text: '测试文本',
        );

        // Assert
        expect(result, taskId);
        expect(aiProvider.error, isNull);
      });

      test('should track task progress', () async {
        // Arrange
        const taskId = 'progress_task_123';
        final taskStatus = AITaskStatus(
          taskId: taskId,
          taskType: 'photo_restore',
          status: 'processing',
          progress: AITaskProgress(
            currentStep: 1,
            totalSteps: 3,
            percentage: 33.3,
            stepDescription: '正在处理图片',
          ),
        );

        when(mockAIService.getTaskStatus(taskId))
            .thenAnswer((_) async => taskStatus);

        // Act
        final result = await aiProvider.getTaskStatus(taskId);

        // Assert
        expect(result?.taskId, taskId);
        expect(result?.isProcessing, true);
        expect(result?.progress.percentage, 33.3);
      });

      test('should cancel task successfully', () async {
        // Arrange
        const taskId = 'cancel_task_123';

        when(mockAIService.cancelTask(taskId))
            .thenAnswer((_) async => Future.value());

        // Act
        final result = await aiProvider.cancelTask(taskId);

        // Assert
        expect(result, true);
        expect(aiProvider.error, isNull);
      });
    });

    group('Widget Tests', () {
      testWidgets('AiServicesScreen should display correctly', (tester) async {
        // Arrange
        await tester.pumpWidget(
          CupertinoApp(
            home: ChangeNotifierProvider<AIProvider>.value(
              value: aiProvider,
              child: const AiServicesScreen(),
            ),
          ),
        );

        // Act
        await tester.pump();

        // Assert
        expect(find.text('AI 服务'), findsOneWidget);
        expect(find.text('AI 照片修复'), findsOneWidget);
        expect(find.text('AI 声音克隆'), findsOneWidget);
        expect(find.text('任务管理'), findsOneWidget);
      });

      testWidgets('AiPhotoRepairScreen should display operation selector',
          (tester) async {
        // Arrange
        await tester.pumpWidget(
          CupertinoApp(
            home: ChangeNotifierProvider<AIProvider>.value(
              value: aiProvider,
              child: const AiPhotoRepairScreen(),
            ),
          ),
        );

        // Act
        await tester.pump();

        // Assert
        expect(find.text('AI 照片修复'), findsOneWidget);
        expect(find.text('选择处理类型'), findsOneWidget);
        expect(find.text('照片修复'), findsOneWidget);
        expect(find.text('黑白上色'), findsOneWidget);
        expect(find.text('分辨率增强'), findsOneWidget);
        expect(find.text('背景移除'), findsOneWidget);
      });

      testWidgets('AiVoiceCloningScreen should display voice file selector',
          (tester) async {
        // Arrange
        await tester.pumpWidget(
          CupertinoApp(
            home: ChangeNotifierProvider<AIProvider>.value(
              value: aiProvider,
              child: const AiVoiceCloningScreen(),
            ),
          ),
        );

        // Act
        await tester.pump();

        // Assert
        expect(find.text('AI 语音克隆'), findsOneWidget);
        expect(find.text('选择语音样本'), findsOneWidget);
        expect(find.text('点击选择音频文件'), findsOneWidget);
        expect(find.text('输入要生成的文本'), findsOneWidget);
      });

      testWidgets('AiTaskListScreen should display task filters',
          (tester) async {
        // Arrange
        await tester.pumpWidget(
          CupertinoApp(
            home: ChangeNotifierProvider<AIProvider>.value(
              value: aiProvider,
              child: const AiTaskListScreen(),
            ),
          ),
        );

        // Act
        await tester.pump();

        // Assert
        expect(find.text('AI 任务'), findsOneWidget);
        expect(find.text('全部'), findsOneWidget);
        expect(find.text('进行中'), findsOneWidget);
        expect(find.text('已完成'), findsOneWidget);
        expect(find.text('失败'), findsOneWidget);
      });
    });

    group('Integration Tests', () {
      testWidgets('should navigate between AI screens', (tester) async {
        // Arrange
        await tester.pumpWidget(
          CupertinoApp(
            home: ChangeNotifierProvider<AIProvider>.value(
              value: aiProvider,
              child: const AiServicesScreen(),
            ),
          ),
        );

        // Act - 点击照片修复
        await tester.tap(find.text('AI 照片修复'));
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('选择处理类型'), findsOneWidget);

        // Act - 返回
        await tester.tap(find.byType(CupertinoNavigationBarBackButton));
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('AI 服务'), findsOneWidget);
      });

      testWidgets('should handle error states gracefully', (tester) async {
        // Arrange
        aiProvider.setError('网络连接失败');

        await tester.pumpWidget(
          CupertinoApp(
            home: ChangeNotifierProvider<AIProvider>.value(
              value: aiProvider,
              child: const AiServicesScreen(),
            ),
          ),
        );

        // Act
        await tester.pump();

        // Assert
        expect(find.text('网络连接失败'), findsOneWidget);
      });
    });

    group('Performance Tests', () {
      testWidgets('should render large task list efficiently', (tester) async {
        // Arrange
        final largeTasks = List.generate(
          100,
          (index) => AITaskStatus(
            taskId: 'task_$index',
            taskType: 'photo_restore',
            status: 'completed',
            progress: AITaskProgress(
              currentStep: 1,
              totalSteps: 1,
              percentage: 100.0,
              stepDescription: '已完成',
            ),
          ),
        );

        // Mock large task list
        when(mockAIService.getUserTasks()).thenAnswer((_) async => largeTasks);

        await tester.pumpWidget(
          CupertinoApp(
            home: ChangeNotifierProvider<AIProvider>.value(
              value: aiProvider,
              child: const AiTaskListScreen(),
            ),
          ),
        );

        // Act
        final stopwatch = Stopwatch()..start();
        await tester.pump();
        stopwatch.stop();

        // Assert - 渲染时间应该小于100ms
        expect(stopwatch.elapsedMilliseconds, lessThan(100));
      });

      test('should handle memory efficiently', () {
        // Arrange
        final provider = AIProvider(mockAIService);

        // Act - 创建大量任务状态
        for (int i = 0; i < 1000; i++) {
          provider.tasks; // 访问任务列表
        }

        // Assert - 应该没有内存泄漏
        expect(provider.tasks.length, lessThanOrEqualTo(1000));
      });
    });

    group('Error Handling Tests', () {
      test('should handle network timeouts', () async {
        // Arrange
        when(mockAIService.checkHealth())
            .thenThrow(AIServiceException('网络超时'));

        // Act
        await aiProvider.checkHealth();

        // Assert
        expect(aiProvider.error, contains('网络超时'));
      });

      test('should handle invalid file formats', () async {
        // Arrange
        final invalidFile = File('test.txt');
        when(mockAIService.batchProcessPhotos(
          files: [invalidFile],
          operation: 'restore',
        )).thenThrow(AIServiceException('不支持的文件格式'));

        // Act
        final result = await aiProvider.batchProcessPhotos(
          files: [invalidFile],
          operation: 'restore',
        );

        // Assert
        expect(result, isNull);
        expect(aiProvider.error, contains('不支持的文件格式'));
      });
    });
  });
}

// 扩展AIProvider以支持测试
extension AIProviderTest on AIProvider {
  void setError(String error) {
    _setError(error);
  }

  void _setError(String error) {
    // 这里需要访问私有方法，在实际实现中可能需要调整
    // 或者添加测试专用的方法
  }
}
