app/crud.py:1131: error: Name "can_access_memorial_space" already defined on line 595  [no-redef]
app/api_v1_routers/payments.py:449: error: Unsupported right operand type for in ("object")  [operator]
app/ai_services/voice_service.py:196: error: Unsupported operand types for < ("float" and "object")  [operator]
app/ai_services/voice_service.py:201: error: Unsupported operand types for < ("float" and "object")  [operator]
app/ai_services/voice_service.py:281: error: Argument "target_sample_rate" to "enhance_audio_quality" of "VoiceQualityEnhancer" has incompatible type "object"; expected "int"  [arg-type]
app/ai_services/voice_service.py:400: error: Argument "key" to "sort" of "list" has incompatible type "Callable[[dict[str, object]], object]"; expected "Callable[[dict[str, object]], SupportsDunderLT[Any] | SupportsDunderGT[Any]]"  [arg-type]
app/ai_services/voice_service.py:400: error: Incompatible return value type (got "object", expected "SupportsDunderLT[Any] | SupportsDunderGT[Any]")  [return-value]
app/ai_services/ai_task_manager.py:154: error: Statement is unreachable  [unreachable]
app/ai_services/ai_task_manager.py:239: error: Statement is unreachable  [unreachable]
app/ai_services/ai_task_manager.py:265: error: Statement is unreachable  [unreachable]
app/monitoring/performance_monitor.py:148: error: Statement is unreachable  [unreachable]
app/monitoring/performance_monitor.py:364: error: Statement is unreachable  [unreachable]
app/monitoring/performance_monitor.py:383: error: Statement is unreachable  [unreachable]
app/ai_services/batch_processor.py:13: error: Library stubs not installed for "aiofiles"  [import]
app/ai_services/batch_processor.py:13: note: Hint: "python3 -m pip install types-aiofiles"
app/ai_services/batch_processor.py:13: note: (or run "mypy --install-types" to install all missing stub packages)
app/ai_services/batch_processor.py:13: note: See https://mypy.readthedocs.io/en/stable/running_mypy.html#missing-imports
app/ai_services/batch_processor.py:350: error: Incompatible types in assignment (expression has type "float", variable has type "int")  [assignment]
app/api_v1_routers/ai_enhanced.py:126: error: Name "replicate_ai_service" is not defined  [name-defined]
app/api_v1_routers/ai_enhanced.py:407: error: Item "str" of "str | None" has no attribute "HTTP_500_INTERNAL_SERVER_ERROR"  [union-attr]
app/api_v1_routers/ai_enhanced.py:437: error: Name "replicate_ai_service" is not defined  [name-defined]
app/api_v1_routers/ai_enhanced.py:499: error: Name "replicate_ai_service" is not defined  [name-defined]
app/api_v1_routers/ai_enhanced.py:550: error: Name "replicate_ai_service" is not defined  [name-defined]
app/api_v1_routers/ai_enhanced.py:845: error: Unsupported target for indexed assignment ("object")  [index]
app/api_v1_routers/ai_enhanced.py:851: error: Unsupported target for indexed assignment ("object")  [index]
app/api_v1_routers/ai_enhanced.py:860: error: Statement is unreachable  [unreachable]
app/api_v1_routers/ai_enhanced.py:867: error: Unsupported target for indexed assignment ("object")  [index]
app/api_v1_routers/ai_enhanced.py:873: error: Unsupported target for indexed assignment ("object")  [index]
app/api_v1_routers/ai_enhanced.py:883: error: Unsupported target for indexed assignment ("object")  [index]
app/api_v1_routers/ai_enhanced.py:889: error: Unsupported target for indexed assignment ("object")  [index]
Found 27 errors in 7 files (checked 66 source files)
