app/ai_services/ai_task_manager.py:6:1: I001 [*] Import block is un-sorted or un-formatted
   |
 4 |   """
 5 |
 6 | / import asyncio
 7 | | import json
 8 | | import logging
 9 | | import time
10 | | import uuid
11 | | from datetime import datetime, timedelta
12 | | from enum import Enum
13 | | from pathlib import Path
14 | | from typing import Any, Dict, List, Optional, Callable
15 | | from dataclasses import dataclass, asdict
16 | | from fastapi import BackgroundTasks
   | |___________________________________^ I001
17 |
18 |   # 可选的 Redis 依赖
   |
   = help: Organize imports

app/ai_services/ai_task_manager.py:9:8: F401 [*] `time` imported but unused
   |
 7 | import json
 8 | import logging
 9 | import time
   |        ^^^^ F401
10 | import uuid
11 | from datetime import datetime, timedelta
   |
   = help: Remove unused import: `time`

app/ai_services/ai_task_manager.py:13:21: F401 [*] `pathlib.Path` imported but unused
   |
11 | from datetime import datetime, timedelta
12 | from enum import Enum
13 | from pathlib import Path
   |                     ^^^^ F401
14 | from typing import Any, Dict, List, Optional, Callable
15 | from dataclasses import dataclass, asdict
   |
   = help: Remove unused import: `pathlib.Path`

app/ai_services/ai_task_manager.py:14:1: UP035 [*] Import from `collections.abc` instead: `Callable`
   |
12 | from enum import Enum
13 | from pathlib import Path
14 | from typing import Any, Dict, List, Optional, Callable
   | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP035
15 | from dataclasses import dataclass, asdict
16 | from fastapi import BackgroundTasks
   |
   = help: Import from `collections.abc`

app/ai_services/ai_task_manager.py:14:1: UP035 `typing.Dict` is deprecated, use `dict` instead
   |
12 | from enum import Enum
13 | from pathlib import Path
14 | from typing import Any, Dict, List, Optional, Callable
   | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP035
15 | from dataclasses import dataclass, asdict
16 | from fastapi import BackgroundTasks
   |

app/ai_services/ai_task_manager.py:14:1: UP035 `typing.List` is deprecated, use `list` instead
   |
12 | from enum import Enum
13 | from pathlib import Path
14 | from typing import Any, Dict, List, Optional, Callable
   | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP035
15 | from dataclasses import dataclass, asdict
16 | from fastapi import BackgroundTasks
   |

app/ai_services/ai_task_manager.py:16:21: F401 [*] `fastapi.BackgroundTasks` imported but unused
   |
14 | from typing import Any, Dict, List, Optional, Callable
15 | from dataclasses import dataclass, asdict
16 | from fastapi import BackgroundTasks
   |                     ^^^^^^^^^^^^^^^ F401
17 |
18 | # 可选的 Redis 依赖
   |
   = help: Remove unused import: `fastapi.BackgroundTasks`

app/ai_services/ai_task_manager.py:57:31: UP007 [*] Use `X | Y` for type annotations
   |
55 |     step_description: str = ""
56 |     percentage: float = 0.0
57 |     estimated_time_remaining: Optional[int] = None  # 秒
   |                               ^^^^^^^^^^^^^ UP007
   |
   = help: Convert to `X | Y`

app/ai_services/ai_task_manager.py:66:14: UP007 [*] Use `X | Y` for type annotations
   |
64 |     task_type: TaskType
65 |     status: TaskStatus
66 |     user_id: Optional[str] = None
   |              ^^^^^^^^^^^^^ UP007
67 |     input_data: Dict[str, Any] = None
68 |     output_data: Dict[str, Any] = None
   |
   = help: Convert to `X | Y`

app/ai_services/ai_task_manager.py:67:17: UP006 [*] Use `dict` instead of `Dict` for type annotation
   |
65 |     status: TaskStatus
66 |     user_id: Optional[str] = None
67 |     input_data: Dict[str, Any] = None
   |                 ^^^^ UP006
68 |     output_data: Dict[str, Any] = None
69 |     progress: TaskProgress = None
   |
   = help: Replace with `dict`

app/ai_services/ai_task_manager.py:68:18: UP006 [*] Use `dict` instead of `Dict` for type annotation
   |
66 |     user_id: Optional[str] = None
67 |     input_data: Dict[str, Any] = None
68 |     output_data: Dict[str, Any] = None
   |                  ^^^^ UP006
69 |     progress: TaskProgress = None
70 |     error_message: Optional[str] = None
   |
   = help: Replace with `dict`

app/ai_services/ai_task_manager.py:70:20: UP007 [*] Use `X | Y` for type annotations
   |
68 |     output_data: Dict[str, Any] = None
69 |     progress: TaskProgress = None
70 |     error_message: Optional[str] = None
   |                    ^^^^^^^^^^^^^ UP007
71 |     created_at: datetime = None
72 |     started_at: Optional[datetime] = None
   |
   = help: Convert to `X | Y`

app/ai_services/ai_task_manager.py:72:17: UP007 [*] Use `X | Y` for type annotations
   |
70 |     error_message: Optional[str] = None
71 |     created_at: datetime = None
72 |     started_at: Optional[datetime] = None
   |                 ^^^^^^^^^^^^^^^^^^ UP007
73 |     completed_at: Optional[datetime] = None
74 |     priority: int = 1  # 1-10, 10为最高优先级
   |
   = help: Convert to `X | Y`

app/ai_services/ai_task_manager.py:73:19: UP007 [*] Use `X | Y` for type annotations
   |
71 |     created_at: datetime = None
72 |     started_at: Optional[datetime] = None
73 |     completed_at: Optional[datetime] = None
   |                   ^^^^^^^^^^^^^^^^^^ UP007
74 |     priority: int = 1  # 1-10, 10为最高优先级
75 |     retry_count: int = 0
   |
   = help: Convert to `X | Y`

app/ai_services/ai_task_manager.py:95:31: UP006 [*] Use `dict` instead of `Dict` for type annotation
   |
93 |         self.redis_url = redis_url
94 |         self.redis_client = None
95 |         self.task_processors: Dict[TaskType, Callable] = {}
   |                               ^^^^ UP006
96 |         self.running_tasks: Dict[str, asyncio.Task] = {}
97 |         self.max_concurrent_tasks = 5
   |
   = help: Replace with `dict`

app/ai_services/ai_task_manager.py:96:29: UP006 [*] Use `dict` instead of `Dict` for type annotation
   |
94 |         self.redis_client = None
95 |         self.task_processors: Dict[TaskType, Callable] = {}
96 |         self.running_tasks: Dict[str, asyncio.Task] = {}
   |                             ^^^^ UP006
97 |         self.max_concurrent_tasks = 5
98 |         self.task_timeout = 600  # 10分钟超时
   |
   = help: Replace with `dict`

app/ai_services/ai_task_manager.py:101:29: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
100 |         # 内存存储作为后备
101 |         self._memory_tasks: Dict[str, AITask] = {}
    |                             ^^^^ UP006
102 |
103 |     async def initialize(self):
    |
    = help: Replace with `dict`

app/ai_services/ai_task_manager.py:128:21: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
126 |         self, 
127 |         task_type: TaskType, 
128 |         input_data: Dict[str, Any],
    |                     ^^^^ UP006
129 |         user_id: Optional[str] = None,
130 |         priority: int = 1
    |
    = help: Replace with `dict`

app/ai_services/ai_task_manager.py:129:18: UP007 [*] Use `X | Y` for type annotations
    |
127 |         task_type: TaskType, 
128 |         input_data: Dict[str, Any],
129 |         user_id: Optional[str] = None,
    |                  ^^^^^^^^^^^^^ UP007
130 |         priority: int = 1
131 |     ) -> str:
    |
    = help: Convert to `X | Y`

app/ai_services/ai_task_manager.py:151:47: UP007 [*] Use `X | Y` for type annotations
    |
149 |         return task_id
150 |     
151 |     async def get_task(self, task_id: str) -> Optional[AITask]:
    |                                               ^^^^^^^^^^^^^^^^ UP007
152 |         """获取任务信息"""
153 |         if self.redis_client:
    |
    = help: Convert to `X | Y`

app/ai_services/ai_task_manager.py:181:35: UP007 [*] Use `X | Y` for type annotations
    |
179 |         total_steps: int,
180 |         step_description: str = "",
181 |         estimated_time_remaining: Optional[int] = None
    |                                   ^^^^^^^^^^^^^ UP007
182 |     ):
183 |         """更新任务进度"""
    |
    = help: Convert to `X | Y`

app/ai_services/ai_task_manager.py:193:62: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
191 |             await self._save_task(task)
192 |     
193 |     async def complete_task(self, task_id: str, output_data: Dict[str, Any]):
    |                                                              ^^^^ UP006
194 |         """完成任务"""
195 |         task = await self.get_task(task_id)
    |
    = help: Replace with `dict`

app/ai_services/ai_task_manager.py:232:17: UP007 [*] Use `X | Y` for type annotations
    |
230 |         self,
231 |         user_id: str,
232 |         status: Optional[TaskStatus] = None,
    |                 ^^^^^^^^^^^^^^^^^^^^ UP007
233 |         limit: int = 50
234 |     ) -> List[AITask]:
    |
    = help: Convert to `X | Y`

app/ai_services/ai_task_manager.py:234:10: UP006 [*] Use `list` instead of `List` for type annotation
    |
232 |         status: Optional[TaskStatus] = None,
233 |         limit: int = 50
234 |     ) -> List[AITask]:
    |          ^^^^ UP006
235 |         """获取用户的任务列表"""
236 |         tasks = []
    |
    = help: Replace with `list`

app/ai_services/ai_task_manager.py:338:16: UP041 [*] Replace aliased errors with `TimeoutError`
    |
336 |             await self.complete_task(task_id, result)
337 |             
338 |         except asyncio.TimeoutError:
    |                ^^^^^^^^^^^^^^^^^^^^ UP041
339 |             await self.fail_task(task_id, "任务执行超时")
340 |         except asyncio.CancelledError:
    |
    = help: Replace `asyncio.TimeoutError` with builtin `TimeoutError`

app/ai_services/batch_processor.py:6:1: I001 [*] Import block is un-sorted or un-formatted
   |
 4 |   """
 5 |
 6 | / import asyncio
 7 | | import logging
 8 | | import time
 9 | | from pathlib import Path
10 | | from typing import List, Dict, Any, Optional, BinaryIO
11 | | from dataclasses import dataclass
12 | | from concurrent.futures import ThreadPoolExecutor
13 | | import aiofiles
14 | |
15 | | from .replicate_service import ReplicateService
16 | | from .ai_task_manager import AITaskManager, TaskType, task_manager
   | |__________________________________________________________________^ I001
17 |
18 |   logger = logging.getLogger(__name__)
   |
   = help: Organize imports

app/ai_services/batch_processor.py:10:1: UP035 `typing.List` is deprecated, use `list` instead
   |
 8 | import time
 9 | from pathlib import Path
10 | from typing import List, Dict, Any, Optional, BinaryIO
   | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP035
11 | from dataclasses import dataclass
12 | from concurrent.futures import ThreadPoolExecutor
   |

app/ai_services/batch_processor.py:10:1: UP035 `typing.Dict` is deprecated, use `dict` instead
   |
 8 | import time
 9 | from pathlib import Path
10 | from typing import List, Dict, Any, Optional, BinaryIO
   | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP035
11 | from dataclasses import dataclass
12 | from concurrent.futures import ThreadPoolExecutor
   |

app/ai_services/batch_processor.py:10:47: F401 [*] `typing.BinaryIO` imported but unused
   |
 8 | import time
 9 | from pathlib import Path
10 | from typing import List, Dict, Any, Optional, BinaryIO
   |                                               ^^^^^^^^ F401
11 | from dataclasses import dataclass
12 | from concurrent.futures import ThreadPoolExecutor
   |
   = help: Remove unused import: `typing.BinaryIO`

app/ai_services/batch_processor.py:13:8: F401 [*] `aiofiles` imported but unused
   |
11 | from dataclasses import dataclass
12 | from concurrent.futures import ThreadPoolExecutor
13 | import aiofiles
   |        ^^^^^^^^ F401
14 |
15 | from .replicate_service import ReplicateService
   |
   = help: Remove unused import: `aiofiles`

app/ai_services/batch_processor.py:16:30: F401 [*] `.ai_task_manager.AITaskManager` imported but unused
   |
15 | from .replicate_service import ReplicateService
16 | from .ai_task_manager import AITaskManager, TaskType, task_manager
   |                              ^^^^^^^^^^^^^ F401
17 |
18 | logger = logging.getLogger(__name__)
   |
   = help: Remove unused import

app/ai_services/batch_processor.py:16:45: F401 [*] `.ai_task_manager.TaskType` imported but unused
   |
15 | from .replicate_service import ReplicateService
16 | from .ai_task_manager import AITaskManager, TaskType, task_manager
   |                                             ^^^^^^^^ F401
17 |
18 | logger = logging.getLogger(__name__)
   |
   = help: Remove unused import

app/ai_services/batch_processor.py:26:14: UP006 [*] Use `dict` instead of `Dict` for type annotation
   |
24 |     file_path: str
25 |     filename: str
26 |     options: Dict[str, Any] = None
   |              ^^^^ UP006
27 |     result: Optional[Dict[str, Any]] = None
28 |     error: Optional[str] = None
   |
   = help: Replace with `dict`

app/ai_services/batch_processor.py:27:13: UP007 [*] Use `X | Y` for type annotations
   |
25 |     filename: str
26 |     options: Dict[str, Any] = None
27 |     result: Optional[Dict[str, Any]] = None
   |             ^^^^^^^^^^^^^^^^^^^^^^^^ UP007
28 |     error: Optional[str] = None
29 |     processing_time: Optional[float] = None
   |
   = help: Convert to `X | Y`

app/ai_services/batch_processor.py:27:22: UP006 [*] Use `dict` instead of `Dict` for type annotation
   |
25 |     filename: str
26 |     options: Dict[str, Any] = None
27 |     result: Optional[Dict[str, Any]] = None
   |                      ^^^^ UP006
28 |     error: Optional[str] = None
29 |     processing_time: Optional[float] = None
   |
   = help: Replace with `dict`

app/ai_services/batch_processor.py:28:12: UP007 [*] Use `X | Y` for type annotations
   |
26 |     options: Dict[str, Any] = None
27 |     result: Optional[Dict[str, Any]] = None
28 |     error: Optional[str] = None
   |            ^^^^^^^^^^^^^ UP007
29 |     processing_time: Optional[float] = None
   |
   = help: Convert to `X | Y`

app/ai_services/batch_processor.py:29:22: UP007 [*] Use `X | Y` for type annotations
   |
27 |     result: Optional[Dict[str, Any]] = None
28 |     error: Optional[str] = None
29 |     processing_time: Optional[float] = None
   |                      ^^^^^^^^^^^^^^^ UP007
   |
   = help: Convert to `X | Y`

app/ai_services/batch_processor.py:41:14: UP006 [*] Use `list` instead of `List` for type annotation
   |
39 |     success_rate: float
40 |     total_processing_time: float
41 |     results: List[BatchItem]
   |              ^^^^ UP006
42 |     errors: List[str]
   |
   = help: Replace with `list`

app/ai_services/batch_processor.py:42:13: UP006 [*] Use `list` instead of `List` for type annotation
   |
40 |     total_processing_time: float
41 |     results: List[BatchItem]
42 |     errors: List[str]
   |             ^^^^ UP006
   |
   = help: Replace with `list`

app/ai_services/batch_processor.py:56:21: UP006 [*] Use `list` instead of `List` for type annotation
   |
54 |         self,
55 |         task_id: str,
56 |         file_paths: List[str],
   |                     ^^^^ UP006
57 |         operation: str,
58 |         options: Dict[str, Any] = None
   |
   = help: Replace with `list`

app/ai_services/batch_processor.py:58:18: UP006 [*] Use `dict` instead of `Dict` for type annotation
   |
56 |         file_paths: List[str],
57 |         operation: str,
58 |         options: Dict[str, Any] = None
   |                  ^^^^ UP006
59 |     ) -> BatchResult:
60 |         """
   |
   = help: Replace with `dict`

app/ai_services/batch_processor.py:186:16: UP006 [*] Use `list` instead of `List` for type annotation
    |
184 |         task_id: str,
185 |         voice_sample_path: str,
186 |         texts: List[str],
    |                ^^^^ UP006
187 |         language: str = "zh"
188 |     ) -> BatchResult:
    |
    = help: Replace with `list`

app/ai_services/batch_processor.py:284:10: UP007 [*] Use `X | Y` for type annotations
    |
282 |         total: int, 
283 |         elapsed_time: float
284 |     ) -> Optional[int]:
    |          ^^^^^^^^^^^^^ UP007
285 |         """估算剩余时间"""
286 |         if completed == 0:
    |
    = help: Convert to `X | Y`

app/ai_services/batch_processor.py:297:21: UP006 [*] Use `list` instead of `List` for type annotation
    |
295 |     async def optimize_batch_processing(
296 |         self,
297 |         file_paths: List[str],
    |                     ^^^^ UP006
298 |         operation: str
299 |     ) -> Dict[str, Any]:
    |
    = help: Replace with `list`

app/ai_services/batch_processor.py:299:10: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
297 |         file_paths: List[str],
298 |         operation: str
299 |     ) -> Dict[str, Any]:
    |          ^^^^ UP006
300 |         """
301 |         优化批量处理策略
    |
    = help: Replace with `dict`

app/ai_services/batch_processor.py:370:10: UP006 [*] Use `list` instead of `List` for type annotation
    |
368 |         avg_file_size: float, 
369 |         operation: str
370 |     ) -> List[str]:
    |          ^^^^ UP006
371 |         """获取优化建议"""
372 |         tips = []
    |
    = help: Replace with `list`

app/ai_services/voice_service.py:7:8: F401 [*] `os` imported but unused
  |
6 | import logging
7 | import os
  |        ^^ F401
8 | import tempfile
9 | import time
  |
  = help: Remove unused import: `os`

app/ai_services/voice_service.py:8:8: F401 [*] `tempfile` imported but unused
   |
 6 | import logging
 7 | import os
 8 | import tempfile
   |        ^^^^^^^^ F401
 9 | import time
10 | import uuid
   |
   = help: Remove unused import: `tempfile`

app/ai_services/voice_service.py:12:1: UP035 `typing.Dict` is deprecated, use `dict` instead
   |
10 | import uuid
11 | from pathlib import Path
12 | from typing import Any, BinaryIO, Dict, List, Optional, Tuple
   | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP035
13 |
14 | logger = logging.getLogger(__name__)
   |

app/ai_services/voice_service.py:12:1: UP035 `typing.List` is deprecated, use `list` instead
   |
10 | import uuid
11 | from pathlib import Path
12 | from typing import Any, BinaryIO, Dict, List, Optional, Tuple
   | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP035
13 |
14 | logger = logging.getLogger(__name__)
   |

app/ai_services/voice_service.py:12:1: UP035 `typing.Tuple` is deprecated, use `tuple` instead
   |
10 | import uuid
11 | from pathlib import Path
12 | from typing import Any, BinaryIO, Dict, List, Optional, Tuple
   | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP035
13 |
14 | logger = logging.getLogger(__name__)
   |

app/ai_services/voice_service.py:12:25: F401 [*] `typing.BinaryIO` imported but unused
   |
10 | import uuid
11 | from pathlib import Path
12 | from typing import Any, BinaryIO, Dict, List, Optional, Tuple
   |                         ^^^^^^^^ F401
13 |
14 | logger = logging.getLogger(__name__)
   |
   = help: Remove unused import

app/ai_services/voice_service.py:12:47: F401 [*] `typing.Optional` imported but unused
   |
10 | import uuid
11 | from pathlib import Path
12 | from typing import Any, BinaryIO, Dict, List, Optional, Tuple
   |                                               ^^^^^^^^ F401
13 |
14 | logger = logging.getLogger(__name__)
   |
   = help: Remove unused import

app/ai_services/voice_service.py:12:57: F401 [*] `typing.Tuple` imported but unused
   |
10 | import uuid
11 | from pathlib import Path
12 | from typing import Any, BinaryIO, Dict, List, Optional, Tuple
   |                                                         ^^^^^ F401
13 |
14 | logger = logging.getLogger(__name__)
   |
   = help: Remove unused import

app/ai_services/voice_service.py:18:5: I001 [*] Import block is un-sorted or un-formatted
   |
16 |   # 音频处理库 - 如果不可用则使用简化版本
17 |   try:
18 | /     import librosa
19 | |     import soundfile as sf
20 | |     import numpy as np
21 | |     from pydub import AudioSegment
22 | |     from pydub.effects import normalize, compress_dynamic_range
23 | |     import noisereduce as nr
   | |____________________________^ I001
24 |       AUDIO_LIBS_AVAILABLE = True
25 |   except ImportError:
   |
   = help: Organize imports

app/ai_services/voice_service.py:18:12: F401 `librosa` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
16 | # 音频处理库 - 如果不可用则使用简化版本
17 | try:
18 |     import librosa
   |            ^^^^^^^ F401
19 |     import soundfile as sf
20 |     import numpy as np
   |
   = help: Remove unused import: `librosa`

app/ai_services/voice_service.py:19:25: F401 `soundfile` imported but unused; consider using `importlib.util.find_spec` to test for availability
   |
17 | try:
18 |     import librosa
19 |     import soundfile as sf
   |                         ^^ F401
20 |     import numpy as np
21 |     from pydub import AudioSegment
   |
   = help: Remove unused import: `soundfile`

app/ai_services/voice_service.py:29:1: E402 Module level import not at top of file
   |
27 |     logger.warning("音频处理库不可用，将使用简化版本")
28 |
29 | from .replicate_service import ReplicateService
   | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ E402
   |

app/ai_services/voice_service.py:168:10: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
166 |         audio_path: str, 
167 |         language: str = 'zh'
168 |     ) -> Dict[str, Any]:
    |          ^^^^ UP006
169 |         """
170 |         验证语音样本质量
    |
    = help: Replace with `dict`

app/ai_services/voice_service.py:251:10: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
249 |         speed: float = 1.0,
250 |         pitch_shift: float = 0.0
251 |     ) -> Dict[str, Any]:
    |          ^^^^ UP006
252 |         """
253 |         增强语音克隆
    |
    = help: Replace with `dict`

app/ai_services/voice_service.py:318:23: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
316 |     def _post_process_audio(
317 |         self,
318 |         clone_result: Dict[str, Any],
    |                       ^^^^ UP006
319 |         speed: float,
320 |         pitch_shift: float
    |
    = help: Replace with `dict`

app/ai_services/voice_service.py:321:10: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
319 |         speed: float,
320 |         pitch_shift: float
321 |     ) -> Dict[str, Any]:
    |          ^^^^ UP006
322 |         """
323 |         后处理生成的音频
    |
    = help: Replace with `dict`

app/ai_services/voice_service.py:351:58: UP006 [*] Use `list` instead of `List` for type annotation
    |
349 |             return clone_result
350 |     
351 |     def get_language_recommendations(self, text: str) -> List[Dict[str, Any]]:
    |                                                          ^^^^ UP006
352 |         """
353 |         根据文本内容推荐语言
    |
    = help: Replace with `list`

app/ai_services/voice_service.py:351:63: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
349 |             return clone_result
350 |     
351 |     def get_language_recommendations(self, text: str) -> List[Dict[str, Any]]:
    |                                                               ^^^^ UP006
352 |         """
353 |         根据文本内容推荐语言
    |
    = help: Replace with `dict`

app/ai_services/voice_service.py:405:41: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
404 |     @classmethod
405 |     def get_supported_languages(cls) -> Dict[str, Dict[str, Any]]:
    |                                         ^^^^ UP006
406 |         """获取支持的语言列表"""
407 |         return cls.SUPPORTED_LANGUAGES.copy()
    |
    = help: Replace with `dict`

app/ai_services/voice_service.py:405:51: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
404 |     @classmethod
405 |     def get_supported_languages(cls) -> Dict[str, Dict[str, Any]]:
    |                                                   ^^^^ UP006
406 |         """获取支持的语言列表"""
407 |         return cls.SUPPORTED_LANGUAGES.copy()
    |
    = help: Replace with `dict`

app/ai_services/voice_service.py:419:24: UP006 [*] Use `list` instead of `List` for type annotation
    |
417 |         self,
418 |         user_id: str,
419 |         voice_samples: List[str],
    |                        ^^^^ UP006
420 |         profile_name: str,
421 |         language: str = 'zh'
    |
    = help: Replace with `list`

app/ai_services/voice_service.py:422:10: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
420 |         profile_name: str,
421 |         language: str = 'zh'
422 |     ) -> Dict[str, Any]:
    |          ^^^^ UP006
423 |         """
424 |         创建个性化语音档案
    |
    = help: Replace with `dict`

app/ai_services/voice_service.py:472:53: UP006 [*] Use `list` instead of `List` for type annotation
    |
470 |             raise
471 |     
472 |     def _calculate_quality_score(self, validations: List[Dict[str, Any]]) -> float:
    |                                                     ^^^^ UP006
473 |         """计算语音质量评分"""
474 |         if not validations:
    |
    = help: Replace with `list`

app/ai_services/voice_service.py:472:58: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
470 |             raise
471 |     
472 |     def _calculate_quality_score(self, validations: List[Dict[str, Any]]) -> float:
    |                                                          ^^^^ UP006
473 |         """计算语音质量评分"""
474 |         if not validations:
    |
    = help: Replace with `dict`

app/api_v1_routers/ai.py:1:1: I001 [*] Import block is un-sorted or un-formatted
   |
 1 | / import logging
 2 | | import os
 3 | | import uuid
 4 | | from pathlib import Path
 5 | |
 6 | | from fastapi import APIRouter, Depends, File, Form, HTTPException, UploadFile, status
 7 | |
 8 | | from app.ai_services.replicate_service import ReplicateService
 9 | | from app.core.config import settings
10 | | from app.api_v1_routers.deps import get_current_active_user
11 | | from app.models_sqlalchemy import User  # 导入 User 模型
12 | | from app.schemas_pydantic.ai import AIResultResponse
   | |____________________________________________________^ I001
13 |
14 |   logger = logging.getLogger(__name__)
   |
   = help: Organize imports

app/api_v1_routers/ai_enhanced.py:2:1: I001 [*] Import block is un-sorted or un-formatted
   |
 1 |   # 增强AI功能路由 - Phase 1.2 优化版本
 2 | / import logging
 3 | | import os
 4 | | import time
 5 | | import uuid
 6 | | import asyncio
 7 | | from pathlib import Path
 8 | | from typing import Dict, Any, List, Optional
 9 | |
10 | | from fastapi import APIRouter, Depends, File, Form, HTTPException, UploadFile, status, BackgroundTasks
11 | | from fastapi.responses import JSONResponse
12 | |
13 | | from app.ai_services.replicate_service import ReplicateService
14 | | from app.ai_services.ai_task_manager import AITaskManager, TaskType, TaskStatus, task_manager
15 | | from app.ai_services.batch_processor import BatchProcessor
16 | | from app.ai_services.voice_service import MultiLanguageVoiceCloner, VoicePersonalizationService
17 | | from app.core.config import settings
18 | | from app.api_v1_routers.deps import get_current_active_user
19 | | from app.models_sqlalchemy import User
   | |______________________________________^ I001
20 |
21 |   logger = logging.getLogger(__name__)
   |
   = help: Organize imports

app/api_v1_routers/ai_enhanced.py:8:1: UP035 `typing.Dict` is deprecated, use `dict` instead
   |
 6 | import asyncio
 7 | from pathlib import Path
 8 | from typing import Dict, Any, List, Optional
   | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP035
 9 |
10 | from fastapi import APIRouter, Depends, File, Form, HTTPException, UploadFile, status, BackgroundTasks
   |

app/api_v1_routers/ai_enhanced.py:8:1: UP035 `typing.List` is deprecated, use `list` instead
   |
 6 | import asyncio
 7 | from pathlib import Path
 8 | from typing import Dict, Any, List, Optional
   | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP035
 9 |
10 | from fastapi import APIRouter, Depends, File, Form, HTTPException, UploadFile, status, BackgroundTasks
   |

app/api_v1_routers/ai_enhanced.py:14:45: F401 [*] `app.ai_services.ai_task_manager.AITaskManager` imported but unused
   |
13 | from app.ai_services.replicate_service import ReplicateService
14 | from app.ai_services.ai_task_manager import AITaskManager, TaskType, TaskStatus, task_manager
   |                                             ^^^^^^^^^^^^^ F401
15 | from app.ai_services.batch_processor import BatchProcessor
16 | from app.ai_services.voice_service import MultiLanguageVoiceCloner, VoicePersonalizationService
   |
   = help: Remove unused import: `app.ai_services.ai_task_manager.AITaskManager`

app/api_v1_routers/ai_enhanced.py:126:24: F821 Undefined name `replicate_ai_service`
    |
125 |         # 调用Replicate AI服务
126 |         result = await replicate_ai_service.restore_photo(original_url)
    |                        ^^^^^^^^^^^^^^^^^^^^ F821
127 |
128 |         if result["success"]:
    |

app/api_v1_routers/ai_enhanced.py:158:12: UP006 [*] Use `list` instead of `List` for type annotation
    |
156 | @router.post("/batch/photo-process", summary="批量照片处理")
157 | async def batch_photo_process(
158 |     files: List[UploadFile] = File(..., description="要处理的照片文件列表"),
    |            ^^^^ UP006
159 |     operation: str = Form(..., description="处理操作 (restore, colorize, enhance, remove_bg)"),
160 |     scale: Optional[int] = Form(4, description="增强倍数 (仅用于enhance)", ge=2, le=8),
    |
    = help: Replace with `list`

app/api_v1_routers/ai_enhanced.py:160:12: UP007 [*] Use `X | Y` for type annotations
    |
158 |     files: List[UploadFile] = File(..., description="要处理的照片文件列表"),
159 |     operation: str = Form(..., description="处理操作 (restore, colorize, enhance, remove_bg)"),
160 |     scale: Optional[int] = Form(4, description="增强倍数 (仅用于enhance)", ge=2, le=8),
    |            ^^^^^^^^^^^^^ UP007
161 |     face_enhance: Optional[bool] = Form(True, description="是否增强面部 (仅用于enhance)"),
162 |     background_tasks: BackgroundTasks = BackgroundTasks(),
    |
    = help: Convert to `X | Y`

app/api_v1_routers/ai_enhanced.py:161:19: UP007 [*] Use `X | Y` for type annotations
    |
159 |     operation: str = Form(..., description="处理操作 (restore, colorize, enhance, remove_bg)"),
160 |     scale: Optional[int] = Form(4, description="增强倍数 (仅用于enhance)", ge=2, le=8),
161 |     face_enhance: Optional[bool] = Form(True, description="是否增强面部 (仅用于enhance)"),
    |                   ^^^^^^^^^^^^^^ UP007
162 |     background_tasks: BackgroundTasks = BackgroundTasks(),
163 |     current_user: User = Depends(get_current_active_user),
    |
    = help: Convert to `X | Y`

app/api_v1_routers/ai_enhanced.py:354:13: UP007 [*] Use `X | Y` for type annotations
    |
352 | @router.get("/tasks", summary="获取用户任务列表")
353 | async def get_user_tasks(
354 |     status: Optional[str] = None,
    |             ^^^^^^^^^^^^^ UP007
355 |     limit: int = 50,
356 |     current_user: User = Depends(get_current_active_user),
    |
    = help: Convert to `X | Y`

app/api_v1_routers/ai_enhanced.py:368:17: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
366 |                   task_status = TaskStatus(status)
367 |               except ValueError:
368 | /                 raise HTTPException(
369 | |                     status_code=status.HTTP_400_BAD_REQUEST,
370 | |                     detail=f"无效的任务状态: {status}"
371 | |                 )
    | |_________________^ B904
372 |
373 |           tasks = await task_manager.get_user_tasks(
    |

app/api_v1_routers/ai_enhanced.py:437:24: F821 Undefined name `replicate_ai_service`
    |
436 |         # 调用Replicate AI服务
437 |         result = await replicate_ai_service.enhance_photo(
    |                        ^^^^^^^^^^^^^^^^^^^^ F821
438 |             original_url, scale=scale, face_enhance=face_enhance
439 |         )
    |

app/api_v1_routers/ai_enhanced.py:499:24: F821 Undefined name `replicate_ai_service`
    |
498 |         # 调用Replicate AI服务
499 |         result = await replicate_ai_service.remove_background(original_url, model=model)
    |                        ^^^^^^^^^^^^^^^^^^^^ F821
500 |
501 |         if result["success"]:
    |

app/api_v1_routers/ai_enhanced.py:550:24: F821 Undefined name `replicate_ai_service`
    |
549 |         # 调用Replicate AI服务
550 |         result = await replicate_ai_service.colorize_photo(original_url)
    |                        ^^^^^^^^^^^^^^^^^^^^ F821
551 |
552 |         if result["success"]:
    |

app/api_v1_routers/ai_enhanced.py:1048:67: UP006 [*] Use `dict` instead of `Dict` for type annotation
     |
1047 |         # 注册批量照片处理器
1048 |         async def batch_photo_processor(task_id: str, input_data: Dict[str, Any]) -> Dict[str, Any]:
     |                                                                   ^^^^ UP006
1049 |             batch_processor = get_batch_processor()
1050 |             result = await batch_processor.process_batch_photos(
     |
     = help: Replace with `dict`

app/api_v1_routers/ai_enhanced.py:1048:86: UP006 [*] Use `dict` instead of `Dict` for type annotation
     |
1047 |         # 注册批量照片处理器
1048 |         async def batch_photo_processor(task_id: str, input_data: Dict[str, Any]) -> Dict[str, Any]:
     |                                                                                      ^^^^ UP006
1049 |             batch_processor = get_batch_processor()
1050 |             result = await batch_processor.process_batch_photos(
     |
     = help: Replace with `dict`

app/api_v1_routers/ai_enhanced.py:1063:67: UP006 [*] Use `dict` instead of `Dict` for type annotation
     |
1062 |         # 注册语音克隆处理器
1063 |         async def voice_clone_processor(task_id: str, input_data: Dict[str, Any]) -> Dict[str, Any]:
     |                                                                   ^^^^ UP006
1064 |             voice_cloner = get_voice_cloner()
1065 |             result = voice_cloner.clone_voice_enhanced(
     |
     = help: Replace with `dict`

app/api_v1_routers/ai_enhanced.py:1063:86: UP006 [*] Use `dict` instead of `Dict` for type annotation
     |
1062 |         # 注册语音克隆处理器
1063 |         async def voice_clone_processor(task_id: str, input_data: Dict[str, Any]) -> Dict[str, Any]:
     |                                                                                      ^^^^ UP006
1064 |             voice_cloner = get_voice_cloner()
1065 |             result = voice_cloner.clone_voice_enhanced(
     |
     = help: Replace with `dict`

app/api_v1_routers/ai_enhanced.py:1076:67: UP006 [*] Use `dict` instead of `Dict` for type annotation
     |
1075 |         # 注册批量语音克隆处理器
1076 |         async def batch_voice_processor(task_id: str, input_data: Dict[str, Any]) -> Dict[str, Any]:
     |                                                                   ^^^^ UP006
1077 |             batch_processor = get_batch_processor()
1078 |             result = await batch_processor.process_batch_voice_clone(
     |
     = help: Replace with `dict`

app/api_v1_routers/ai_enhanced.py:1076:86: UP006 [*] Use `dict` instead of `Dict` for type annotation
     |
1075 |         # 注册批量语音克隆处理器
1076 |         async def batch_voice_processor(task_id: str, input_data: Dict[str, Any]) -> Dict[str, Any]:
     |                                                                                      ^^^^ UP006
1077 |             batch_processor = get_batch_processor()
1078 |             result = await batch_processor.process_batch_voice_clone(
     |
     = help: Replace with `dict`

app/api_v1_routers/ai_enhanced.py:1101:1: E402 Module level import not at top of file
     |
1100 | # 在应用启动时注册处理器
1101 | import asyncio
     | ^^^^^^^^^^^^^^ E402
1102 | asyncio.create_task(register_task_processors())
     |

app/api_v1_routers/ai_enhanced.py:1101:1: I001 [*] Import block is un-sorted or un-formatted
     |
1100 | # 在应用启动时注册处理器
1101 | import asyncio
     | ^^^^^^^^^^^^^^ I001
1102 | asyncio.create_task(register_task_processors())
     |
     = help: Organize imports

app/api_v1_routers/ai_enhanced.py:1101:8: F811 [*] Redefinition of unused `asyncio` from line 6
     |
1100 | # 在应用启动时注册处理器
1101 | import asyncio
     |        ^^^^^^^ F811
1102 | asyncio.create_task(register_task_processors())
     |
     = help: Remove definition: `asyncio`

app/api_v1_routers/analytics.py:1:1: UP035 `typing.Dict` is deprecated, use `dict` instead
  |
1 | from typing import Dict, Any, Optional
  | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP035
2 | from fastapi import APIRouter, Depends, HTTPException, status, Query
3 | from sqlalchemy.orm import Session
  |

app/api_v1_routers/analytics.py:1:1: I001 [*] Import block is un-sorted or un-formatted
  |
1 | / from typing import Dict, Any, Optional
2 | | from fastapi import APIRouter, Depends, HTTPException, status, Query
3 | | from sqlalchemy.orm import Session
4 | | from app.db.session import get_db
5 | | from app.dependencies import get_current_user
6 | | from app.models_sqlalchemy import User
7 | | from app.services.analytics_service import AnalyticsService
  | |___________________________________________________________^ I001
8 |
9 |   router = APIRouter()
  |
  = help: Organize imports

app/api_v1_routers/analytics.py:1:20: F401 [*] `typing.Dict` imported but unused
  |
1 | from typing import Dict, Any, Optional
  |                    ^^^^ F401
2 | from fastapi import APIRouter, Depends, HTTPException, status, Query
3 | from sqlalchemy.orm import Session
  |
  = help: Remove unused import

app/api_v1_routers/analytics.py:1:26: F401 [*] `typing.Any` imported but unused
  |
1 | from typing import Dict, Any, Optional
  |                          ^^^ F401
2 | from fastapi import APIRouter, Depends, HTTPException, status, Query
3 | from sqlalchemy.orm import Session
  |
  = help: Remove unused import

app/api_v1_routers/analytics.py:1:31: F401 [*] `typing.Optional` imported but unused
  |
1 | from typing import Dict, Any, Optional
  |                               ^^^^^^^^ F401
2 | from fastapi import APIRouter, Depends, HTTPException, status, Query
3 | from sqlalchemy.orm import Session
  |
  = help: Remove unused import

app/api_v1_routers/analytics.py:35:9: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
   |
33 |           return {"success": True, "data": analytics_data, "date_range": date_range}
34 |       except Exception as e:
35 | /         raise HTTPException(
36 | |             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
37 | |             detail=f"获取商业分析数据失败: {str(e)}",
38 | |         )
   | |_________^ B904
   |

app/api_v1_routers/analytics.py:56:9: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
   |
54 |           }
55 |       except Exception as e:
56 | /         raise HTTPException(
57 | |             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
58 | |             detail=f"获取收入分析数据失败: {str(e)}",
59 | |         )
   | |_________^ B904
   |

app/api_v1_routers/analytics.py:77:9: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
   |
75 |           }
76 |       except Exception as e:
77 | /         raise HTTPException(
78 | |             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
79 | |             detail=f"获取用户分析数据失败: {str(e)}",
80 | |         )
   | |_________^ B904
   |

app/api_v1_routers/analytics.py:98:9: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
 96 |           }
 97 |       except Exception as e:
 98 | /         raise HTTPException(
 99 | |             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
100 | |             detail=f"获取转化分析数据失败: {str(e)}",
101 | |         )
    | |_________^ B904
    |

app/api_v1_routers/analytics.py:119:9: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
117 |           }
118 |       except Exception as e:
119 | /         raise HTTPException(
120 | |             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
121 | |             detail=f"获取留存分析数据失败: {str(e)}",
122 | |         )
    | |_________^ B904
    |

app/api_v1_routers/analytics.py:140:9: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
138 |           }
139 |       except Exception as e:
140 | /         raise HTTPException(
141 | |             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
142 | |             detail=f"获取产品分析数据失败: {str(e)}",
143 | |         )
    | |_________^ B904
    |

app/api_v1_routers/analytics.py:161:9: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
159 |           }
160 |       except Exception as e:
161 | /         raise HTTPException(
162 | |             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
163 | |             detail=f"获取增长分析数据失败: {str(e)}",
164 | |         )
    | |_________^ B904
    |

app/api_v1_routers/analytics.py:182:9: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
180 |           }
181 |       except Exception as e:
182 | /         raise HTTPException(
183 | |             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
184 | |             detail=f"获取会员分析数据失败: {str(e)}",
185 | |         )
    | |_________^ B904
    |

app/api_v1_routers/analytics.py:198:9: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
196 |           return {"success": True, "data": recommendations}
197 |       except Exception as e:
198 | /         raise HTTPException(
199 | |             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
200 | |             detail=f"获取会员优化建议失败: {str(e)}",
201 | |         )
    | |_________^ B904
    |

app/api_v1_routers/analytics.py:274:9: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
272 |           return {"success": True, "data": summary}
273 |       except Exception as e:
274 | /         raise HTTPException(
275 | |             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
276 | |             detail=f"获取仪表板概览失败: {str(e)}",
277 | |         )
    | |_________^ B904
    |

app/api_v1_routers/analytics.py:315:9: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
313 |           raise
314 |       except Exception as e:
315 | /         raise HTTPException(
316 | |             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
317 | |             detail=f"导出分析数据失败: {str(e)}",
318 | |         )
    | |_________^ B904
    |

app/api_v1_routers/auth.py:18:5: F401 [*] `app.core.security.get_password_hash` imported but unused
   |
16 |     create_email_verification_token,
17 |     create_password_reset_token,
18 |     get_password_hash,
   |     ^^^^^^^^^^^^^^^^^ F401
19 |     verify_email_verification_token,
20 |     verify_password_reset_token,
   |
   = help: Remove unused import: `app.core.security.get_password_hash`

app/api_v1_routers/membership.py:1:1: UP035 `typing.Dict` is deprecated, use `dict` instead
  |
1 | from typing import Dict, Any, Optional
  | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP035
2 | from fastapi import APIRouter, Depends, HTTPException, status
3 | from sqlalchemy.orm import Session
  |

app/api_v1_routers/membership.py:1:1: I001 [*] Import block is un-sorted or un-formatted
   |
 1 | / from typing import Dict, Any, Optional
 2 | | from fastapi import APIRouter, Depends, HTTPException, status
 3 | | from sqlalchemy.orm import Session
 4 | | from app.db.session import get_db
 5 | | from app.dependencies import get_current_user
 6 | | from app.models_sqlalchemy import User
 7 | | from app.services.membership_service import MembershipService, FeatureType
 8 | | from app.schemas_pydantic.membership import (
 9 | |     MembershipSummaryResponse,
10 | |     FeatureAccessCheck,
11 | |     OperationValidation,
12 | |     UsageUpdateRequest,
13 | | )
   | |_^ I001
14 |
15 |   router = APIRouter()
   |
   = help: Organize imports

app/api_v1_routers/membership.py:1:20: F401 [*] `typing.Dict` imported but unused
  |
1 | from typing import Dict, Any, Optional
  |                    ^^^^ F401
2 | from fastapi import APIRouter, Depends, HTTPException, status
3 | from sqlalchemy.orm import Session
  |
  = help: Remove unused import

app/api_v1_routers/membership.py:1:26: F401 [*] `typing.Any` imported but unused
  |
1 | from typing import Dict, Any, Optional
  |                          ^^^ F401
2 | from fastapi import APIRouter, Depends, HTTPException, status
3 | from sqlalchemy.orm import Session
  |
  = help: Remove unused import

app/api_v1_routers/membership.py:33:9: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
   |
31 |           return MembershipSummaryResponse(**summary)
32 |       except Exception as e:
33 | /         raise HTTPException(
34 | |             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
35 | |             detail=f"获取会员信息失败: {str(e)}",
36 | |         )
   | |_________^ B904
   |

app/api_v1_routers/membership.py:76:9: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
   |
74 |           raise
75 |       except Exception as e:
76 | /         raise HTTPException(
77 | |             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
78 | |             detail=f"检查功能权限失败: {str(e)}",
79 | |         )
   | |_________^ B904
   |

app/api_v1_routers/membership.py:85:19: UP007 [*] Use `X | Y` for type annotations
   |
83 | async def validate_operation(
84 |     operation_type: str,
85 |     file_size_gb: Optional[float] = None,
   |                   ^^^^^^^^^^^^^^^ UP007
86 |     minutes: Optional[int] = None,
87 |     current_user: User = Depends(get_current_user),
   |
   = help: Convert to `X | Y`

app/api_v1_routers/membership.py:86:14: UP007 [*] Use `X | Y` for type annotations
   |
84 |     operation_type: str,
85 |     file_size_gb: Optional[float] = None,
86 |     minutes: Optional[int] = None,
   |              ^^^^^^^^^^^^^ UP007
87 |     current_user: User = Depends(get_current_user),
88 |     membership_service: MembershipService = Depends(get_membership_service),
   |
   = help: Convert to `X | Y`

app/api_v1_routers/membership.py:105:9: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
103 |           return OperationValidation(**validation_result)
104 |       except Exception as e:
105 | /         raise HTTPException(
106 | |             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
107 | |             detail=f"验证操作权限失败: {str(e)}",
108 | |         )
    | |_________^ B904
    |

app/api_v1_routers/membership.py:132:9: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
130 |           raise
131 |       except Exception as e:
132 | /         raise HTTPException(
133 | |             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
134 | |             detail=f"更新使用量失败: {str(e)}",
135 | |         )
    | |_________^ B904
    |

app/api_v1_routers/membership.py:148:9: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
146 |           return {"usage": usage}
147 |       except Exception as e:
148 | /         raise HTTPException(
149 | |             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
150 | |             detail=f"获取使用情况失败: {str(e)}",
151 | |         )
    | |_________^ B904
    |

app/api_v1_routers/membership.py:166:9: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
164 |           return {"recommendations": recommendations}
165 |       except Exception as e:
166 | /         raise HTTPException(
167 | |             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
168 | |             detail=f"获取升级建议失败: {str(e)}",
169 | |         )
    | |_________^ B904
    |

app/api_v1_routers/membership.py:210:9: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
208 |           return {"tiers": tiers_info}
209 |       except Exception as e:
210 | /         raise HTTPException(
211 | |             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
212 | |             detail=f"获取等级信息失败: {str(e)}",
213 | |         )
    | |_________^ B904
    |

app/api_v1_routers/memorial_spaces.py:52:43: E712 Avoid equality comparisons to `True`; use `Scene.is_active:` for truth checks
   |
50 |         scene = (
51 |             db.query(Scene)
52 |             .filter(Scene.id == scene_id, Scene.is_active == True)
   |                                           ^^^^^^^^^^^^^^^^^^^^^^^ E712
53 |             .first()
54 |         )
   |
   = help: Replace with `Scene.is_active`

app/api_v1_routers/memorial_spaces.py:260:13: E712 Avoid equality comparisons to `True`; use `models.MemorialSpace.is_active:` for truth checks
    |
258 |         .filter(
259 |             models.MemorialSpace.privacy_level == "public",
260 |             models.MemorialSpace.is_active == True,
    |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ E712
261 |         )
262 |         .scalar()
    |
    = help: Replace with `models.MemorialSpace.is_active`

app/api_v1_routers/memorial_spaces.py:271:17: E712 Avoid equality comparisons to `True`; use `models.MemorialSpace.is_active:` for truth checks
    |
269 |             .filter(
270 |                 models.MemorialSpace.privacy_level == "public",
271 |                 models.MemorialSpace.is_active == True,
    |                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ E712
272 |                 models.MemorialSpace.deceased_name.ilike(f"%{search}%"),
273 |             )
    |
    = help: Replace with `models.MemorialSpace.is_active`

app/api_v1_routers/messages.py:1:1: I001 [*] Import block is un-sorted or un-formatted
   |
 1 | / from uuid import UUID
 2 | |
 3 | | from fastapi import APIRouter, Depends, HTTPException, status
 4 | | from sqlalchemy.orm import Session
 5 | |
 6 | | from app import crud
 7 | | from app import schemas_pydantic as schemas
 8 | | from app.api_v1_routers.deps import get_current_user, get_db
 9 | | from app.models_sqlalchemy import User
10 | | from app.services.content_moderation import moderate_message_content, ModerationAction
   | |______________________________________________________________________________________^ I001
11 |
12 |   router = APIRouter()
   |
   = help: Organize imports

app/api_v1_routers/monitoring.py:6:1: I001 [*] Import block is un-sorted or un-formatted
   |
 4 |   """
 5 |
 6 | / import logging
 7 | | from typing import Dict, Any, Optional
 8 | | from fastapi import APIRouter, Depends, HTTPException, Query, status
 9 | | from fastapi.responses import JSONResponse
10 | |
11 | | from app.monitoring.performance_monitor import performance_monitor
12 | | from app.api_v1_routers.deps import get_current_active_user
13 | | from app.models_sqlalchemy import User
   | |______________________________________^ I001
14 |
15 |   logger = logging.getLogger(__name__)
   |
   = help: Organize imports

app/api_v1_routers/monitoring.py:7:1: UP035 `typing.Dict` is deprecated, use `dict` instead
  |
6 | import logging
7 | from typing import Dict, Any, Optional
  | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP035
8 | from fastapi import APIRouter, Depends, HTTPException, Query, status
9 | from fastapi.responses import JSONResponse
  |

app/api_v1_routers/monitoring.py:7:20: F401 [*] `typing.Dict` imported but unused
  |
6 | import logging
7 | from typing import Dict, Any, Optional
  |                    ^^^^ F401
8 | from fastapi import APIRouter, Depends, HTTPException, Query, status
9 | from fastapi.responses import JSONResponse
  |
  = help: Remove unused import

app/api_v1_routers/monitoring.py:7:26: F401 [*] `typing.Any` imported but unused
  |
6 | import logging
7 | from typing import Dict, Any, Optional
  |                          ^^^ F401
8 | from fastapi import APIRouter, Depends, HTTPException, Query, status
9 | from fastapi.responses import JSONResponse
  |
  = help: Remove unused import

app/api_v1_routers/monitoring.py:7:31: F401 [*] `typing.Optional` imported but unused
  |
6 | import logging
7 | from typing import Dict, Any, Optional
  |                               ^^^^^^^^ F401
8 | from fastapi import APIRouter, Depends, HTTPException, Query, status
9 | from fastapi.responses import JSONResponse
  |
  = help: Remove unused import

app/api_v1_routers/monitoring.py:47:9: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
   |
45 |       except Exception as e:
46 |           logger.error(f"获取系统健康状态失败: {e}")
47 | /         raise HTTPException(
48 | |             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
49 | |             detail=f"获取系统健康状态失败: {str(e)}"
50 | |         )
   | |_________^ B904
   |

app/api_v1_routers/monitoring.py:101:9: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
 99 |       except Exception as e:
100 |           logger.error(f"获取质量指标失败: {e}")
101 | /         raise HTTPException(
102 | |             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
103 | |             detail=f"获取质量指标失败: {str(e)}"
104 | |         )
    | |_________^ B904
    |

app/api_v1_routers/monitoring.py:142:9: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
140 |       except Exception as e:
141 |           logger.error(f"获取端点统计失败: {e}")
142 | /         raise HTTPException(
143 | |             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
144 | |             detail=f"获取端点统计失败: {str(e)}"
145 | |         )
    | |_________^ B904
    |

app/api_v1_routers/monitoring.py:175:9: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
173 |       except Exception as e:
174 |           logger.error(f"获取性能趋势失败: {e}")
175 | /         raise HTTPException(
176 | |             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
177 | |             detail=f"获取性能趋势失败: {str(e)}"
178 | |         )
    | |_________^ B904
    |

app/api_v1_routers/monitoring.py:261:9: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
259 |       except Exception as e:
260 |           logger.error(f"获取监控仪表板失败: {e}")
261 | /         raise HTTPException(
262 | |             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
263 | |             detail=f"获取监控仪表板失败: {str(e)}"
264 | |         )
    | |_________^ B904
    |

app/api_v1_routers/monitoring.py:294:9: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
292 |       except Exception as e:
293 |           logger.error(f"告警系统测试失败: {e}")
294 | /         raise HTTPException(
295 | |             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
296 | |             detail=f"告警系统测试失败: {str(e)}"
297 | |         )
    | |_________^ B904
    |

app/api_v1_routers/monitoring.py:343:9: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
341 |       except Exception as e:
342 |           logger.error(f"获取监控状态失败: {e}")
343 | /         raise HTTPException(
344 | |             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
345 | |             detail=f"获取监控状态失败: {str(e)}"
346 | |         )
    | |_________^ B904
    |

app/api_v1_routers/payments.py:7:1: UP035 `typing.List` is deprecated, use `list` instead
  |
5 | """
6 |
7 | from typing import List, Optional, Dict, Any
  | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP035
8 | from fastapi import APIRouter, Depends, HTTPException, Query
9 | from sqlalchemy.orm import Session
  |

app/api_v1_routers/payments.py:7:1: UP035 `typing.Dict` is deprecated, use `dict` instead
  |
5 | """
6 |
7 | from typing import List, Optional, Dict, Any
  | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP035
8 | from fastapi import APIRouter, Depends, HTTPException, Query
9 | from sqlalchemy.orm import Session
  |

app/api_v1_routers/payments.py:7:1: I001 [*] Import block is un-sorted or un-formatted
   |
 5 |   """
 6 |
 7 | / from typing import List, Optional, Dict, Any
 8 | | from fastapi import APIRouter, Depends, HTTPException, Query
 9 | | from sqlalchemy.orm import Session
10 | | from pydantic import BaseModel, Field
11 | | from datetime import datetime, timedelta
12 | | import uuid
13 | | import hashlib
14 | | import hmac
15 | | import json
16 | |
17 | | from app.db.session import get_db
18 | | from app.dependencies import get_current_user
19 | | from app.models_sqlalchemy import User
20 | | from app.services.payment_processor import PaymentProcessorService
21 | | from app.services.payment_webhook import PaymentWebhookService
   | |______________________________________________________________^ I001
22 |
23 |   router = APIRouter()
   |
   = help: Organize imports

app/api_v1_routers/payments.py:13:8: F401 [*] `hashlib` imported but unused
   |
11 | from datetime import datetime, timedelta
12 | import uuid
13 | import hashlib
   |        ^^^^^^^ F401
14 | import hmac
15 | import json
   |
   = help: Remove unused import: `hashlib`

app/api_v1_routers/payments.py:14:8: F401 [*] `hmac` imported but unused
   |
12 | import uuid
13 | import hashlib
14 | import hmac
   |        ^^^^ F401
15 | import json
   |
   = help: Remove unused import: `hmac`

app/api_v1_routers/payments.py:15:8: F401 [*] `json` imported but unused
   |
13 | import hashlib
14 | import hmac
15 | import json
   |        ^^^^ F401
16 |
17 | from app.db.session import get_db
   |
   = help: Remove unused import: `json`

app/api_v1_routers/payments.py:20:44: F401 [*] `app.services.payment_processor.PaymentProcessorService` imported but unused
   |
18 | from app.dependencies import get_current_user
19 | from app.models_sqlalchemy import User
20 | from app.services.payment_processor import PaymentProcessorService
   |                                            ^^^^^^^^^^^^^^^^^^^^^^^ F401
21 | from app.services.payment_webhook import PaymentWebhookService
   |
   = help: Remove unused import: `app.services.payment_processor.PaymentProcessorService`

app/api_v1_routers/payments.py:21:42: F401 [*] `app.services.payment_webhook.PaymentWebhookService` imported but unused
   |
19 | from app.models_sqlalchemy import User
20 | from app.services.payment_processor import PaymentProcessorService
21 | from app.services.payment_webhook import PaymentWebhookService
   |                                          ^^^^^^^^^^^^^^^^^^^^^ F401
22 |
23 | router = APIRouter()
   |
   = help: Remove unused import: `app.services.payment_webhook.PaymentWebhookService`

app/api_v1_routers/payments.py:32:24: UP006 [*] Use `list` instead of `List` for type annotation
   |
30 |     name: str
31 |     type: str  # 'domestic' | 'international'
32 |     supported_methods: List[Dict[str, Any]]
   |                        ^^^^ UP006
33 |     enabled: bool
   |
   = help: Replace with `list`

app/api_v1_routers/payments.py:32:29: UP006 [*] Use `dict` instead of `Dict` for type annotation
   |
30 |     name: str
31 |     type: str  # 'domestic' | 'international'
32 |     supported_methods: List[Dict[str, Any]]
   |                             ^^^^ UP006
33 |     enabled: bool
   |
   = help: Replace with `dict`

app/api_v1_routers/payments.py:43:15: UP006 [*] Use `list` instead of `List` for type annotation
   |
41 |     minimum_amount: float
42 |     maximum_amount: float
43 |     currency: List[str]
   |               ^^^^ UP006
   |
   = help: Replace with `list`

app/api_v1_routers/payments.py:53:17: UP007 [*] Use `X | Y` for type annotations
   |
51 |     provider_id: str = Field(..., description="支付提供商ID")
52 |     method_id: str = Field(..., description="支付方式ID")
53 |     return_url: Optional[str] = Field(None, description="支付成功返回URL")
   |                 ^^^^^^^^^^^^^ UP007
54 |     cancel_url: Optional[str] = Field(None, description="支付取消返回URL")
55 |     metadata: Optional[Dict[str, Any]] = Field(None, description="附加数据")
   |
   = help: Convert to `X | Y`

app/api_v1_routers/payments.py:54:17: UP007 [*] Use `X | Y` for type annotations
   |
52 |     method_id: str = Field(..., description="支付方式ID")
53 |     return_url: Optional[str] = Field(None, description="支付成功返回URL")
54 |     cancel_url: Optional[str] = Field(None, description="支付取消返回URL")
   |                 ^^^^^^^^^^^^^ UP007
55 |     metadata: Optional[Dict[str, Any]] = Field(None, description="附加数据")
   |
   = help: Convert to `X | Y`

app/api_v1_routers/payments.py:55:15: UP007 [*] Use `X | Y` for type annotations
   |
53 |     return_url: Optional[str] = Field(None, description="支付成功返回URL")
54 |     cancel_url: Optional[str] = Field(None, description="支付取消返回URL")
55 |     metadata: Optional[Dict[str, Any]] = Field(None, description="附加数据")
   |               ^^^^^^^^^^^^^^^^^^^^^^^^ UP007
   |
   = help: Convert to `X | Y`

app/api_v1_routers/payments.py:55:24: UP006 [*] Use `dict` instead of `Dict` for type annotation
   |
53 |     return_url: Optional[str] = Field(None, description="支付成功返回URL")
54 |     cancel_url: Optional[str] = Field(None, description="支付取消返回URL")
55 |     metadata: Optional[Dict[str, Any]] = Field(None, description="附加数据")
   |                        ^^^^ UP006
   |
   = help: Replace with `dict`

app/api_v1_routers/payments.py:61:19: UP007 [*] Use `X | Y` for type annotations
   |
59 |     payment_id: str
60 |     status: str  # 'pending' | 'completed' | 'failed' | 'cancelled'
61 |     redirect_url: Optional[str] = None
   |                   ^^^^^^^^^^^^^ UP007
62 |     message: Optional[str] = None
63 |     transaction_id: Optional[str] = None
   |
   = help: Convert to `X | Y`

app/api_v1_routers/payments.py:62:14: UP007 [*] Use `X | Y` for type annotations
   |
60 |     status: str  # 'pending' | 'completed' | 'failed' | 'cancelled'
61 |     redirect_url: Optional[str] = None
62 |     message: Optional[str] = None
   |              ^^^^^^^^^^^^^ UP007
63 |     transaction_id: Optional[str] = None
64 |     created_at: datetime
   |
   = help: Convert to `X | Y`

app/api_v1_routers/payments.py:63:21: UP007 [*] Use `X | Y` for type annotations
   |
61 |     redirect_url: Optional[str] = None
62 |     message: Optional[str] = None
63 |     transaction_id: Optional[str] = None
   |                     ^^^^^^^^^^^^^ UP007
64 |     created_at: datetime
   |
   = help: Convert to `X | Y`

app/api_v1_routers/payments.py:70:14: UP007 [*] Use `X | Y` for type annotations
   |
68 |     payment_id: str
69 |     status: str
70 |     message: Optional[str] = None
   |              ^^^^^^^^^^^^^ UP007
71 |     transaction_id: Optional[str] = None
72 |     completed_at: Optional[datetime] = None
   |
   = help: Convert to `X | Y`

app/api_v1_routers/payments.py:71:21: UP007 [*] Use `X | Y` for type annotations
   |
69 |     status: str
70 |     message: Optional[str] = None
71 |     transaction_id: Optional[str] = None
   |                     ^^^^^^^^^^^^^ UP007
72 |     completed_at: Optional[datetime] = None
   |
   = help: Convert to `X | Y`

app/api_v1_routers/payments.py:72:19: UP007 [*] Use `X | Y` for type annotations
   |
70 |     message: Optional[str] = None
71 |     transaction_id: Optional[str] = None
72 |     completed_at: Optional[datetime] = None
   |                   ^^^^^^^^^^^^^^^^^^ UP007
   |
   = help: Convert to `X | Y`

app/api_v1_routers/payments.py:83:19: UP007 [*] Use `X | Y` for type annotations
   |
81 |     payment_method: str
82 |     created_at: datetime
83 |     completed_at: Optional[datetime]
   |                   ^^^^^^^^^^^^^^^^^^ UP007
84 |     description: str
85 |     metadata: Optional[Dict[str, Any]]
   |
   = help: Convert to `X | Y`

app/api_v1_routers/payments.py:85:15: UP007 [*] Use `X | Y` for type annotations
   |
83 |     completed_at: Optional[datetime]
84 |     description: str
85 |     metadata: Optional[Dict[str, Any]]
   |               ^^^^^^^^^^^^^^^^^^^^^^^^ UP007
   |
   = help: Convert to `X | Y`

app/api_v1_routers/payments.py:85:24: UP006 [*] Use `dict` instead of `Dict` for type annotation
   |
83 |     completed_at: Optional[datetime]
84 |     description: str
85 |     metadata: Optional[Dict[str, Any]]
   |                        ^^^^ UP006
   |
   = help: Replace with `dict`

app/api_v1_routers/payments.py:89:15: UP006 [*] Use `list` instead of `List` for type annotation
   |
88 | class PaymentHistoryResponse(BaseModel):
89 |     payments: List[PaymentHistoryItem]
   |               ^^^^ UP006
90 |     total: int
91 |     page: int
   |
   = help: Replace with `list`

app/api_v1_routers/payments.py:98:42: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
 98 | @router.get("/providers", response_model=Dict[str, List[PaymentProviderResponse]])
    |                                          ^^^^ UP006
 99 | async def get_payment_providers(
100 |     db: Session = Depends(get_db), current_user: User = Depends(get_current_user)
    |
    = help: Replace with `dict`

app/api_v1_routers/payments.py:98:52: UP006 [*] Use `list` instead of `List` for type annotation
    |
 98 | @router.get("/providers", response_model=Dict[str, List[PaymentProviderResponse]])
    |                                                    ^^^^ UP006
 99 | async def get_payment_providers(
100 |     db: Session = Depends(get_db), current_user: User = Depends(get_current_user)
    |
    = help: Replace with `list`

app/api_v1_routers/payments.py:214:9: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
213 |     except Exception as e:
214 |         raise HTTPException(status_code=500, detail=f"获取支付提供商失败: {str(e)}")
    |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B904
    |

app/api_v1_routers/payments.py:219:20: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
217 | @router.get(
218 |     "/providers/{provider_id}/methods",
219 |     response_model=Dict[str, List[PaymentMethodResponse]],
    |                    ^^^^ UP006
220 | )
221 | async def get_payment_methods(
    |
    = help: Replace with `dict`

app/api_v1_routers/payments.py:219:30: UP006 [*] Use `list` instead of `List` for type annotation
    |
217 | @router.get(
218 |     "/providers/{provider_id}/methods",
219 |     response_model=Dict[str, List[PaymentMethodResponse]],
    |                              ^^^^ UP006
220 | )
221 | async def get_payment_methods(
    |
    = help: Replace with `list`

app/api_v1_routers/payments.py:276:9: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
275 |     except Exception as e:
276 |         raise HTTPException(status_code=500, detail=f"获取支付方式失败: {str(e)}")
    |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B904
    |

app/api_v1_routers/payments.py:308:9: F841 Local variable `payment_data` is assigned to but never used
    |
307 |         # 创建支付记录（这里应该保存到数据库）
308 |         payment_data = {
    |         ^^^^^^^^^^^^ F841
309 |             "payment_id": payment_id,
310 |             "user_id": current_user.id,
    |
    = help: Remove assignment to unused variable `payment_data`

app/api_v1_routers/payments.py:348:9: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
346 |         raise
347 |     except Exception as e:
348 |         raise HTTPException(status_code=500, detail=f"创建支付失败: {str(e)}")
    |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B904
    |

app/api_v1_routers/payments.py:384:9: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
383 |     except Exception as e:
384 |         raise HTTPException(status_code=500, detail=f"查询支付状态失败: {str(e)}")
    |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B904
    |

app/api_v1_routers/payments.py:404:9: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
403 |     except Exception as e:
404 |         raise HTTPException(status_code=500, detail=f"取消支付失败: {str(e)}")
    |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B904
    |

app/api_v1_routers/payments.py:414:13: UP007 [*] Use `X | Y` for type annotations
    |
412 |     page: int = Query(1, ge=1),
413 |     limit: int = Query(20, ge=1, le=100),
414 |     status: Optional[str] = Query(None),
    |             ^^^^^^^^^^^^^ UP007
415 |     method: Optional[str] = Query(None),
416 |     start_date: Optional[str] = Query(None),
    |
    = help: Convert to `X | Y`

app/api_v1_routers/payments.py:415:13: UP007 [*] Use `X | Y` for type annotations
    |
413 |     limit: int = Query(20, ge=1, le=100),
414 |     status: Optional[str] = Query(None),
415 |     method: Optional[str] = Query(None),
    |             ^^^^^^^^^^^^^ UP007
416 |     start_date: Optional[str] = Query(None),
417 |     end_date: Optional[str] = Query(None),
    |
    = help: Convert to `X | Y`

app/api_v1_routers/payments.py:416:17: UP007 [*] Use `X | Y` for type annotations
    |
414 |     status: Optional[str] = Query(None),
415 |     method: Optional[str] = Query(None),
416 |     start_date: Optional[str] = Query(None),
    |                 ^^^^^^^^^^^^^ UP007
417 |     end_date: Optional[str] = Query(None),
418 |     db: Session = Depends(get_db),
    |
    = help: Convert to `X | Y`

app/api_v1_routers/payments.py:417:15: UP007 [*] Use `X | Y` for type annotations
    |
415 |     method: Optional[str] = Query(None),
416 |     start_date: Optional[str] = Query(None),
417 |     end_date: Optional[str] = Query(None),
    |               ^^^^^^^^^^^^^ UP007
418 |     db: Session = Depends(get_db),
419 |     current_user: User = Depends(get_current_user),
    |
    = help: Convert to `X | Y`

app/api_v1_routers/payments.py:465:9: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
464 |     except Exception as e:
465 |         raise HTTPException(status_code=500, detail=f"获取支付历史失败: {str(e)}")
    |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B904
    |

app/api_v1_routers/payments.py:473:37: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
471 | @router.post("/{payment_id}/callback")
472 | async def handle_payment_callback(
473 |     payment_id: str, callback_data: Dict[str, Any], db: Session = Depends(get_db)
    |                                     ^^^^ UP006
474 | ):
475 |     """处理支付回调（供支付提供商调用）"""
    |
    = help: Replace with `dict`

app/api_v1_routers/payments.py:484:9: F841 Local variable `transaction_id` is assigned to but never used
    |
482 |         # 更新支付状态
483 |         payment_status = callback_data.get("status", "unknown")
484 |         transaction_id = callback_data.get("transaction_id")
    |         ^^^^^^^^^^^^^^ F841
485 |
486 |         # 这里应该更新数据库中的支付记录
    |
    = help: Remove assignment to unused variable `transaction_id`

app/api_v1_routers/payments.py:500:9: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
498 |         raise
499 |     except Exception as e:
500 |         raise HTTPException(status_code=500, detail=f"处理支付回调失败: {str(e)}")
    |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B904
    |

app/api_v1_routers/payments.py:521:38: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
520 | @router.post("/webhook/test")
521 | async def test_webhook(webhook_data: Dict[str, Any], db: Session = Depends(get_db)):
    |                                      ^^^^ UP006
522 |     """测试支付回调（开发环境使用）"""
523 |     try:
    |
    = help: Replace with `dict`

app/api_v1_routers/payments.py:542:9: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
540 |         raise
541 |     except Exception as e:
542 |         raise HTTPException(status_code=500, detail=f"测试回调失败: {str(e)}")
    |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ B904
    |

app/api_v1_routers/permissions.py:233:13: E712 Avoid equality comparisons to `True`; use `crud.models.MemorialSpacePermission.is_active:` for truth checks
    |
231 |         .filter(
232 |             crud.models.MemorialSpacePermission.memorial_space_id == space_id,
233 |             crud.models.MemorialSpacePermission.is_active == True,
    |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ E712
234 |         )
235 |         .all()
    |
    = help: Replace with `crud.models.MemorialSpacePermission.is_active`

app/api_v1_routers/scenes.py:2:1: I001 [*] Import block is un-sorted or un-formatted
   |
 1 |   # 3D场景管理API
 2 | / import logging
 3 | | from typing import Any, List
 4 | | from uuid import UUID
 5 | |
 6 | | from fastapi import APIRouter, Depends, HTTPException, Query, status
 7 | | from sqlalchemy.orm import Session
 8 | | from sqlalchemy.exc import SQLAlchemyError
 9 | |
10 | | from app import crud
11 | | from app import models_sqlalchemy as models
12 | | from app import schemas_pydantic as schemas
13 | | from app.api_v1_routers import deps
14 | | from app.models.scene import Scene, SceneInteractionPoint
15 | | from app.schemas_pydantic.scene import (
16 | |     SceneCreate,
17 | |     SceneListResponse,
18 | |     SceneResponse,
19 | |     SceneUpdate,
20 | |     InteractionPointCreate,
21 | |     InteractionPointResponse,
22 | |     SCENE_TEMPLATES,
23 | | )
   | |_^ I001
24 |
25 |   logger = logging.getLogger(__name__)
   |
   = help: Organize imports

app/api_v1_routers/scenes.py:3:1: UP035 `typing.List` is deprecated, use `list` instead
  |
1 | # 3D场景管理API
2 | import logging
3 | from typing import Any, List
  | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP035
4 | from uuid import UUID
  |

app/api_v1_routers/scenes.py:10:17: F401 [*] `app.crud` imported but unused
   |
 8 | from sqlalchemy.exc import SQLAlchemyError
 9 |
10 | from app import crud
   |                 ^^^^ F401
11 | from app import models_sqlalchemy as models
12 | from app import schemas_pydantic as schemas
   |
   = help: Remove unused import: `app.crud`

app/api_v1_routers/scenes.py:12:37: F401 [*] `app.schemas_pydantic` imported but unused
   |
10 | from app import crud
11 | from app import models_sqlalchemy as models
12 | from app import schemas_pydantic as schemas
   |                                     ^^^^^^^ F401
13 | from app.api_v1_routers import deps
14 | from app.models.scene import Scene, SceneInteractionPoint
   |
   = help: Remove unused import: `app.schemas_pydantic`

app/api_v1_routers/scenes.py:30:33: UP006 [*] Use `list` instead of `List` for type annotation
   |
30 | @router.get("/", response_model=List[SceneListResponse])
   |                                 ^^^^ UP006
31 | def get_scenes(
32 |     *,
   |
   = help: Replace with `list`

app/api_v1_routers/scenes.py:49:40: E712 Avoid equality comparisons to `True`; use `Scene.is_active:` for truth checks
   |
47 |         )
48 |
49 |         query = db.query(Scene).filter(Scene.is_active == True)
   |                                        ^^^^^^^^^^^^^^^^^^^^^^^ E712
50 |
51 |         # 参数验证
   |
   = help: Replace with `Scene.is_active`

app/api_v1_routers/scenes.py:79:9: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
   |
77 |       except SQLAlchemyError as e:
78 |           logger.error(f"数据库查询场景失败: {str(e)}")
79 | /         raise HTTPException(
80 | |             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="获取场景列表失败"
81 | |         )
   | |_________^ B904
82 |       except Exception as e:
83 |           logger.error(f"获取场景列表时发生未知错误: {str(e)}")
   |

app/api_v1_routers/scenes.py:84:9: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
   |
82 |       except Exception as e:
83 |           logger.error(f"获取场景列表时发生未知错误: {str(e)}")
84 | /         raise HTTPException(
85 | |             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="服务器内部错误"
86 | |         )
   | |_________^ B904
   |

app/api_v1_routers/scenes.py:103:43: E712 Avoid equality comparisons to `True`; use `Scene.is_active:` for truth checks
    |
101 |         scene = (
102 |             db.query(Scene)
103 |             .filter(Scene.id == scene_id, Scene.is_active == True)
    |                                           ^^^^^^^^^^^^^^^^^^^^^^^ E712
104 |             .first()
105 |         )
    |
    = help: Replace with `Scene.is_active`

app/api_v1_routers/scenes.py:119:9: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
117 |       except SQLAlchemyError as e:
118 |           logger.error(f"数据库查询场景失败: {str(e)}")
119 | /         raise HTTPException(
120 | |             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="获取场景信息失败"
121 | |         )
    | |_________^ B904
122 |       except Exception as e:
123 |           logger.error(f"获取场景信息时发生未知错误: {str(e)}")
    |

app/api_v1_routers/scenes.py:124:9: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
122 |       except Exception as e:
123 |           logger.error(f"获取场景信息时发生未知错误: {str(e)}")
124 | /         raise HTTPException(
125 | |             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="服务器内部错误"
126 | |         )
    | |_________^ B904
    |

app/api_v1_routers/scenes.py:200:9: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
198 |           db.rollback()
199 |           logger.error(f"数据库创建场景失败: {str(e)}")
200 | /         raise HTTPException(
201 | |             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="创建场景失败"
202 | |         )
    | |_________^ B904
203 |       except Exception as e:
204 |           db.rollback()
    |

app/api_v1_routers/scenes.py:206:9: B904 Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling
    |
204 |           db.rollback()
205 |           logger.error(f"创建场景时发生未知错误: {str(e)}")
206 | /         raise HTTPException(
207 | |             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="服务器内部错误"
208 | |         )
    | |_________^ B904
    |

app/api_v1_routers/scenes.py:270:44: UP006 [*] Use `list` instead of `List` for type annotation
    |
270 | @router.get("/categories/", response_model=List[str])
    |                                            ^^^^ UP006
271 | def get_scene_categories(
272 |     *,
    |
    = help: Replace with `list`

app/api_v1_routers/scenes.py:279:41: E712 Avoid equality comparisons to `True`; use `Scene.is_active:` for truth checks
    |
277 |     """
278 |     categories = (
279 |         db.query(Scene.category).filter(Scene.is_active == True).distinct().all()
    |                                         ^^^^^^^^^^^^^^^^^^^^^^^ E712
280 |     )
281 |     return [category[0] for category in categories]
    |
    = help: Replace with `Scene.is_active`

app/api_v1_routers/scenes.py:302:54: E712 Avoid equality comparisons to `True`; use `Scene.is_active:` for truth checks
    |
300 |     # 验证场景存在
301 |     scene = (
302 |         db.query(Scene).filter(Scene.id == scene_id, Scene.is_active == True).first()
    |                                                      ^^^^^^^^^^^^^^^^^^^^^^^ E712
303 |     )
304 |     if not scene:
    |
    = help: Replace with `Scene.is_active`

app/api_v1_routers/scenes.py:326:55: UP006 [*] Use `list` instead of `List` for type annotation
    |
325 | @router.get(
326 |     "/{scene_id}/interaction-points/", response_model=List[InteractionPointResponse]
    |                                                       ^^^^ UP006
327 | )
328 | def get_scene_interaction_points(
    |
    = help: Replace with `list`

app/api_v1_routers/scenes.py:338:54: E712 Avoid equality comparisons to `True`; use `Scene.is_active:` for truth checks
    |
336 |     # 验证场景存在
337 |     scene = (
338 |         db.query(Scene).filter(Scene.id == scene_id, Scene.is_active == True).first()
    |                                                      ^^^^^^^^^^^^^^^^^^^^^^^ E712
339 |     )
340 |     if not scene:
    |
    = help: Replace with `Scene.is_active`

app/api_v1_routers/scenes.py:349:13: E712 Avoid equality comparisons to `True`; use `SceneInteractionPoint.is_active:` for truth checks
    |
347 |         .filter(
348 |             SceneInteractionPoint.scene_id == scene_id,
349 |             SceneInteractionPoint.is_active == True,
    |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ E712
350 |         )
351 |         .all()
    |
    = help: Replace with `SceneInteractionPoint.is_active`

app/api_v1_routers/scenes.py:375:54: E712 Avoid equality comparisons to `True`; use `Scene.is_active:` for truth checks
    |
373 |     """
374 |     scene = (
375 |         db.query(Scene).filter(Scene.id == scene_id, Scene.is_active == True).first()
    |                                                      ^^^^^^^^^^^^^^^^^^^^^^^ E712
376 |     )
377 |     if not scene:
    |
    = help: Replace with `Scene.is_active`

app/core/security.py:1:1: I001 [*] Import block is un-sorted or un-formatted
   |
 1 | / from datetime import datetime, timedelta
 2 | | from typing import Any
 3 | |
 4 | | from fastapi import Depends, HTTPException, status
 5 | | from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
 6 | | from jose import JWTError, jwt
 7 | | from passlib.context import CryptContext
 8 | | from sqlalchemy.orm import Session
 9 | |
10 | | from app.core.config import settings
   | |____________________________________^ I001
11 |
12 |   pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
   |
   = help: Organize imports

app/core/security.py:4:21: F401 [*] `fastapi.Depends` imported but unused
  |
2 | from typing import Any
3 |
4 | from fastapi import Depends, HTTPException, status
  |                     ^^^^^^^ F401
5 | from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
6 | from jose import JWTError, jwt
  |
  = help: Remove unused import

app/core/security.py:4:30: F401 [*] `fastapi.HTTPException` imported but unused
  |
2 | from typing import Any
3 |
4 | from fastapi import Depends, HTTPException, status
  |                              ^^^^^^^^^^^^^ F401
5 | from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
6 | from jose import JWTError, jwt
  |
  = help: Remove unused import

app/core/security.py:4:45: F401 [*] `fastapi.status` imported but unused
  |
2 | from typing import Any
3 |
4 | from fastapi import Depends, HTTPException, status
  |                                             ^^^^^^ F401
5 | from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
6 | from jose import JWTError, jwt
  |
  = help: Remove unused import

app/core/security.py:5:42: F401 [*] `fastapi.security.HTTPAuthorizationCredentials` imported but unused
  |
4 | from fastapi import Depends, HTTPException, status
5 | from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
  |                                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^ F401
6 | from jose import JWTError, jwt
7 | from passlib.context import CryptContext
  |
  = help: Remove unused import: `fastapi.security.HTTPAuthorizationCredentials`

app/core/security.py:8:28: F401 [*] `sqlalchemy.orm.Session` imported but unused
   |
 6 | from jose import JWTError, jwt
 7 | from passlib.context import CryptContext
 8 | from sqlalchemy.orm import Session
   |                            ^^^^^^^ F401
 9 |
10 | from app.core.config import settings
   |
   = help: Remove unused import: `sqlalchemy.orm.Session`

app/crud.py:200:13: E712 Avoid equality comparisons to `True`; use `models.MemorialSpace.is_active:` for truth checks
    |
198 |         query = db.query(self.model).filter(
199 |             models.MemorialSpace.privacy_level == "public",
200 |             models.MemorialSpace.is_active == True,
    |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ E712
201 |         )
    |
    = help: Replace with `models.MemorialSpace.is_active`

app/crud.py:602:5: C901 `check_memorial_space_access` is too complex (15 > 10)
    |
602 | def check_memorial_space_access(
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^ C901
603 |     db: Session,
604 |     space: models.MemorialSpace,
    |

app/crud.py:779:13: E712 Avoid equality comparisons to `True`; use `models.MemorialSpacePermission.is_active:` for truth checks
    |
777 |             models.MemorialSpacePermission.memorial_space_id == space_id,
778 |             models.MemorialSpacePermission.user_id == user_id,
779 |             models.MemorialSpacePermission.is_active == True,
    |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ E712
780 |         )
781 |         .order_by(models.MemorialSpacePermission.granted_at.desc())
    |
    = help: Replace with `models.MemorialSpacePermission.is_active`

app/crud.py:983:26: C416 Unnecessary dict comprehension (rewrite using `dict()`)
    |
981 |     return {
982 |         "total_tributes": total_tributes,
983 |         "tribute_types": {tribute_type: count for tribute_type, count in tribute_types},
    |                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ C416
984 |         "recent_tributes": recent_tributes,
985 |         "average_duration_seconds": float(avg_duration),
    |
    = help: Rewrite using `dict()`

app/crud.py:1070:13: E712 Avoid equality comparisons to `True`; use `models.MemorialMessage.is_active:` for truth checks
     |
1068 |         .filter(
1069 |             models.MemorialMessage.id == message_id,
1070 |             models.MemorialMessage.is_active == True,
     |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ E712
1071 |         )
1072 |         .first()
     |
     = help: Replace with `models.MemorialMessage.is_active`

app/crud.py:1084:13: E712 Avoid equality comparisons to `True`; use `models.MemorialMessage.is_active:` for truth checks
     |
1082 |         .filter(
1083 |             models.MemorialMessage.memorial_space_id == space_id,
1084 |             models.MemorialMessage.is_active == True,
     |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ E712
1085 |         )
1086 |         .order_by(models.MemorialMessage.created_at.desc())
     |
     = help: Replace with `models.MemorialMessage.is_active`

app/crud.py:1099:13: E712 Avoid equality comparisons to `True`; use `models.MemorialMessage.is_active:` for truth checks
     |
1097 |         .filter(
1098 |             models.MemorialMessage.memorial_space_id == space_id,
1099 |             models.MemorialMessage.is_active == True,
     |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ E712
1100 |         )
1101 |         .scalar()
     |
     = help: Replace with `models.MemorialMessage.is_active`

app/crud.py:1131:5: F811 Redefinition of unused `can_access_memorial_space` from line 595
     |
1131 | def can_access_memorial_space(
     |     ^^^^^^^^^^^^^^^^^^^^^^^^^ F811
1132 |     db: Session, space: models.MemorialSpace, user: models.User | None
1133 | ) -> bool:
     |
     = help: Remove definition: `can_access_memorial_space`

app/db/session.py:1:1: I001 [*] Import block is un-sorted or un-formatted
  |
1 | / from sqlalchemy import create_engine
2 | | from sqlalchemy.orm import sessionmaker, Session
3 | |
4 | | from app.core.config import settings
  | |____________________________________^ I001
5 |
6 |   # Ensure DATABASE_URI is not None
  |
  = help: Organize imports

app/dependencies.py:1:1: I001 [*] Import block is un-sorted or un-formatted
   |
 1 | / from collections.abc import Generator
 2 | |
 3 | | from fastapi import Depends, HTTPException, status
 4 | | from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
 5 | | from jose import JWTError, jwt
 6 | | from sqlalchemy.orm import Session
 7 | |
 8 | | from app.core.config import settings
 9 | | from app.db.session import SessionLocal, get_db
10 | | from app.models_sqlalchemy import User
11 | | from app.core.security import decode_access_token
   | |_________________________________________________^ I001
12 |
13 |   security = HTTPBearer()
   |
   = help: Organize imports

app/dependencies.py:1:29: F401 [*] `collections.abc.Generator` imported but unused
  |
1 | from collections.abc import Generator
  |                             ^^^^^^^^^ F401
2 |
3 | from fastapi import Depends, HTTPException, status
  |
  = help: Remove unused import: `collections.abc.Generator`

app/dependencies.py:5:18: F401 [*] `jose.JWTError` imported but unused
  |
3 | from fastapi import Depends, HTTPException, status
4 | from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
5 | from jose import JWTError, jwt
  |                  ^^^^^^^^ F401
6 | from sqlalchemy.orm import Session
  |
  = help: Remove unused import

app/dependencies.py:5:28: F401 [*] `jose.jwt` imported but unused
  |
3 | from fastapi import Depends, HTTPException, status
4 | from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
5 | from jose import JWTError, jwt
  |                            ^^^ F401
6 | from sqlalchemy.orm import Session
  |
  = help: Remove unused import

app/dependencies.py:8:29: F401 [*] `app.core.config.settings` imported but unused
   |
 6 | from sqlalchemy.orm import Session
 7 |
 8 | from app.core.config import settings
   |                             ^^^^^^^^ F401
 9 | from app.db.session import SessionLocal, get_db
10 | from app.models_sqlalchemy import User
   |
   = help: Remove unused import: `app.core.config.settings`

app/dependencies.py:9:28: F401 [*] `app.db.session.SessionLocal` imported but unused
   |
 8 | from app.core.config import settings
 9 | from app.db.session import SessionLocal, get_db
   |                            ^^^^^^^^^^^^ F401
10 | from app.models_sqlalchemy import User
11 | from app.core.security import decode_access_token
   |
   = help: Remove unused import: `app.db.session.SessionLocal`

app/main.py:1:1: I001 [*] Import block is un-sorted or un-formatted
   |
 1 | / from fastapi import FastAPI
 2 | | from fastapi.middleware.cors import CORSMiddleware
 3 | |
 4 | | from app.api_v1_routers import (
 5 | |     ai,
 6 | |     ai_enhanced,
 7 | |     analytics,
 8 | |     auth,
 9 | |     db_admin,
10 | |     families,
11 | |     memorial_assets,
12 | |     memorial_events,
13 | |     memorial_spaces,
14 | |     membership,
15 | |     messages,
16 | |     permissions,
17 | |     render,
18 | |     scenes,
19 | |     tributes,
20 | |     users,
21 | | )
22 | | from app.core.config import settings
   | |____________________________________^ I001
23 |
24 |   # Import other routers as they are created
   |
   = help: Organize imports

app/models/scene.py:2:1: I001 [*] Import block is un-sorted or un-formatted
   |
 1 |   # 3D场景数据模型
 2 | / import uuid
 3 | | from datetime import datetime
 4 | | from typing import Any
 5 | |
 6 | | from sqlalchemy import (
 7 | |     JSON,
 8 | |     Boolean,
 9 | |     Column,
10 | |     DateTime,
11 | |     String,
12 | |     Text,
13 | |     Integer,
14 | |     ForeignKey,
15 | | )
16 | | from sqlalchemy.dialects.postgresql import UUID
17 | | from sqlalchemy.orm import relationship
18 | |
19 | | from app.models_sqlalchemy import Base
   | |______________________________________^ I001
   |
   = help: Organize imports

app/models_sqlalchemy.py:259:1: E402 Module level import not at top of file
    |
257 | # Scene model is now in app/models/scene.py to avoid duplication
258 | # Import it here for convenience
259 | from app.models.scene import Scene, SceneInteractionPoint
    | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ E402
260 |
261 | # Example for database connection and table creation (typically in main app setup)
    |

app/models_sqlalchemy.py:259:30: F401 [*] `app.models.scene.Scene` imported but unused
    |
257 | # Scene model is now in app/models/scene.py to avoid duplication
258 | # Import it here for convenience
259 | from app.models.scene import Scene, SceneInteractionPoint
    |                              ^^^^^ F401
260 |
261 | # Example for database connection and table creation (typically in main app setup)
    |
    = help: Remove unused import

app/models_sqlalchemy.py:259:37: F401 [*] `app.models.scene.SceneInteractionPoint` imported but unused
    |
257 | # Scene model is now in app/models/scene.py to avoid duplication
258 | # Import it here for convenience
259 | from app.models.scene import Scene, SceneInteractionPoint
    |                                     ^^^^^^^^^^^^^^^^^^^^^ F401
260 |
261 | # Example for database connection and table creation (typically in main app setup)
    |
    = help: Remove unused import

app/monitoring/performance_monitor.py:6:1: I001 [*] Import block is un-sorted or un-formatted
   |
 4 |   """
 5 |
 6 | / import time
 7 | | import asyncio
 8 | | import logging
 9 | | import psutil
10 | | import statistics
11 | | from datetime import datetime, timedelta
12 | | from typing import Dict, List, Optional, Any
13 | | from dataclasses import dataclass, field
14 | | from collections import defaultdict, deque
15 | | from fastapi import Request, Response
16 | | from starlette.middleware.base import BaseHTTPMiddleware
   | |________________________________________________________^ I001
17 |
18 |   # 可选的 Redis 依赖
   |
   = help: Organize imports

app/monitoring/performance_monitor.py:12:1: UP035 `typing.Dict` is deprecated, use `dict` instead
   |
10 | import statistics
11 | from datetime import datetime, timedelta
12 | from typing import Dict, List, Optional, Any
   | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP035
13 | from dataclasses import dataclass, field
14 | from collections import defaultdict, deque
   |

app/monitoring/performance_monitor.py:12:1: UP035 `typing.List` is deprecated, use `list` instead
   |
10 | import statistics
11 | from datetime import datetime, timedelta
12 | from typing import Dict, List, Optional, Any
   | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP035
13 | from dataclasses import dataclass, field
14 | from collections import defaultdict, deque
   |

app/monitoring/performance_monitor.py:13:36: F401 [*] `dataclasses.field` imported but unused
   |
11 | from datetime import datetime, timedelta
12 | from typing import Dict, List, Optional, Any
13 | from dataclasses import dataclass, field
   |                                    ^^^^^ F401
14 | from collections import defaultdict, deque
15 | from fastapi import Request, Response
   |
   = help: Remove unused import: `dataclasses.field`

app/monitoring/performance_monitor.py:15:30: F401 [*] `fastapi.Response` imported but unused
   |
13 | from dataclasses import dataclass, field
14 | from collections import defaultdict, deque
15 | from fastapi import Request, Response
   |                              ^^^^^^^^ F401
16 | from starlette.middleware.base import BaseHTTPMiddleware
   |
   = help: Remove unused import: `fastapi.Response`

app/monitoring/performance_monitor.py:37:14: UP007 [*] Use `X | Y` for type annotations
   |
35 |     endpoint: str
36 |     method: str
37 |     user_id: Optional[str] = None
   |              ^^^^^^^^^^^^^ UP007
38 |     error_message: Optional[str] = None
   |
   = help: Convert to `X | Y`

app/monitoring/performance_monitor.py:38:20: UP007 [*] Use `X | Y` for type annotations
   |
36 |     method: str
37 |     user_id: Optional[str] = None
38 |     error_message: Optional[str] = None
   |                    ^^^^^^^^^^^^^ UP007
   |
   = help: Convert to `X | Y`

app/monitoring/performance_monitor.py:50:22: UP007 [*] Use `X | Y` for type annotations
   |
48 |     disk_usage_percent: float
49 |     active_connections: int
50 |     redis_memory_mb: Optional[float] = None
   |                      ^^^^^^^^^^^^^^^ UP007
   |
   = help: Convert to `X | Y`

app/monitoring/performance_monitor.py:72:35: UP007 [*] Use `X | Y` for type annotations
   |
70 |     """性能监控器"""
71 |     
72 |     def __init__(self, redis_url: Optional[str] = None):
   |                                   ^^^^^^^^^^^^^ UP007
73 |         self.redis_url = redis_url
74 |         self.redis_client = None
   |
   = help: Convert to `X | Y`

app/monitoring/performance_monitor.py:82:30: UP006 [*] Use `dict` instead of `Dict` for type annotation
   |
81 |         # 实时统计
82 |         self.endpoint_stats: Dict[str, List[float]] = defaultdict(list)
   |                              ^^^^ UP006
83 |         self.error_counts: Dict[str, int] = defaultdict(int)
84 |         self.active_requests: int = 0
   |
   = help: Replace with `dict`

app/monitoring/performance_monitor.py:82:40: UP006 [*] Use `list` instead of `List` for type annotation
   |
81 |         # 实时统计
82 |         self.endpoint_stats: Dict[str, List[float]] = defaultdict(list)
   |                                        ^^^^ UP006
83 |         self.error_counts: Dict[str, int] = defaultdict(int)
84 |         self.active_requests: int = 0
   |
   = help: Replace with `list`

app/monitoring/performance_monitor.py:83:28: UP006 [*] Use `dict` instead of `Dict` for type annotation
   |
81 |         # 实时统计
82 |         self.endpoint_stats: Dict[str, List[float]] = defaultdict(list)
83 |         self.error_counts: Dict[str, int] = defaultdict(int)
   |                            ^^^^ UP006
84 |         self.active_requests: int = 0
   |
   = help: Replace with `dict`

app/monitoring/performance_monitor.py:87:32: UP007 [*] Use `X | Y` for type annotations
   |
86 |         # 监控任务
87 |         self._monitoring_task: Optional[asyncio.Task] = None
   |                                ^^^^^^^^^^^^^^^^^^^^^^ UP007
88 |         self._is_monitoring = False
   |
   = help: Convert to `X | Y`

app/monitoring/performance_monitor.py:248:48: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
246 |         )
247 |
248 |     async def get_endpoint_statistics(self) -> Dict[str, Dict[str, Any]]:
    |                                                ^^^^ UP006
249 |         """获取端点统计信息"""
250 |         stats = {}
    |
    = help: Replace with `dict`

app/monitoring/performance_monitor.py:248:58: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
246 |         )
247 |
248 |     async def get_endpoint_statistics(self) -> Dict[str, Dict[str, Any]]:
    |                                                          ^^^^ UP006
249 |         """获取端点统计信息"""
250 |         stats = {}
    |
    = help: Replace with `dict`

app/monitoring/performance_monitor.py:265:42: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
263 |         return stats
264 |
265 |     async def get_system_health(self) -> Dict[str, Any]:
    |                                          ^^^^ UP006
266 |         """获取系统健康状态"""
267 |         if not self.system_metrics:
    |
    = help: Replace with `dict`

app/monitoring/performance_monitor.py:302:63: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
300 |         }
301 |
302 |     async def get_performance_trends(self, hours: int = 1) -> Dict[str, List[Dict[str, Any]]]:
    |                                                               ^^^^ UP006
303 |         """获取性能趋势数据"""
304 |         cutoff_time = datetime.now() - timedelta(hours=hours)
    |
    = help: Replace with `dict`

app/monitoring/performance_monitor.py:302:73: UP006 [*] Use `list` instead of `List` for type annotation
    |
300 |         }
301 |
302 |     async def get_performance_trends(self, hours: int = 1) -> Dict[str, List[Dict[str, Any]]]:
    |                                                                         ^^^^ UP006
303 |         """获取性能趋势数据"""
304 |         cutoff_time = datetime.now() - timedelta(hours=hours)
    |
    = help: Replace with `list`

app/monitoring/performance_monitor.py:302:78: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
300 |         }
301 |
302 |     async def get_performance_trends(self, hours: int = 1) -> Dict[str, List[Dict[str, Any]]]:
    |                                                                              ^^^^ UP006
303 |         """获取性能趋势数据"""
304 |         cutoff_time = datetime.now() - timedelta(hours=hours)
    |
    = help: Replace with `dict`

app/schemas_pydantic/__init__.py:1:1: I001 [*] Import block is un-sorted or un-formatted
    |
  1 | / from pydantic import BaseModel  # Import BaseModel directly
  2 | |
  3 | | from .ai import (
  4 | |     AIResultResponse,
  5 | |     PhotoProcessRequest,
  6 | |     VoiceCloneRequest,
  7 | | )
  8 | | from .ancestor import (
  9 | |     AncestorBase,
 10 | |     AncestorCreate,
 11 | |     AncestorResponse,
 12 | |     AncestorUpdate,
 13 | | )
 14 | | from .environment import (
 15 | |     EnvironmentBase,
 16 | |     EnvironmentCreate,
 17 | |     EnvironmentResponse,
 18 | |     EnvironmentUpdate,
 19 | | )
 20 | | from .family import (
 21 | |     FamilyCreateRequest,
 22 | |     FamilyInvitationCreateRequest,
 23 | |     FamilyInvitationResponse,
 24 | |     FamilyJoinRequest,
 25 | |     FamilyMemberListResponse,
 26 | |     FamilyMemberResponse,
 27 | |     FamilyResponse,
 28 | |     FamilyUpdateRequest,
 29 | |     GenealogyNodeCreateRequest,
 30 | |     GenealogyNodeResponse,
 31 | |     GenealogyNodeUpdateRequest,
 32 | |     GenealogyRelationshipCreateRequest,
 33 | |     GenealogyRelationshipResponse,
 34 | |     GenealogyResponse,
 35 | |     MemorialMessageCreateRequest,
 36 | |     MemorialMessageListResponse,
 37 | |     MemorialMessageResponse,
 38 | |     MemorialMessageUpdateRequest,
 39 | | )
 40 | | from .memorial_asset import (
 41 | |     AssetTypeEnum,
 42 | |     MemorialAssetBase,
 43 | |     MemorialAssetCreate,
 44 | |     MemorialAssetListResponse,  # Added import
 45 | |     MemorialAssetResponse,
 46 | |     MemorialAssetUpdate,
 47 | | )
 48 | | from .memorial_event import (
 49 | |     MemorialEventBase,
 50 | |     MemorialEventCreate,
 51 | |     MemorialEventListResponse,  # Added import
 52 | |     MemorialEventResponse,
 53 | |     MemorialEventUpdate,
 54 | | )
 55 | | from .memorial_space import (
 56 | |     DeceasedGenderEnum,
 57 | |     MemorialCustomSettings,
 58 | |     MemorialSpaceBase,
 59 | |     MemorialSpaceCreate,
 60 | |     MemorialSpaceListResponse,  # Added import
 61 | |     MemorialSpaceResponse,
 62 | |     SceneCustomization,
 63 | |     MemorialSpaceUpdate,
 64 | |     PrivacyLevelEnum,
 65 | | )
 66 | | from .permission import (
 67 | |     AccessCheckRequest,
 68 | |     AccessCheckResponse,
 69 | |     AccessLogEntry,
 70 | |     AccessLogListResponse,
 71 | |     AccessType,
 72 | |     FamilyAccessRequest,
 73 | |     PermissionBase,
 74 | |     PermissionBatchOperation,
 75 | |     PermissionCreate,
 76 | |     PermissionResponse,
 77 | |     PermissionType,
 78 | |     PermissionUpdate,
 79 | |     PrivacySettings,
 80 | | )
 81 | | from .religious_setting import (
 82 | |     ReligiousCulturalSettingBase,
 83 | |     ReligiousCulturalSettingCreate,
 84 | |     ReligiousCulturalSettingResponse,
 85 | |     ReligiousCulturalSettingUpdate,
 86 | | )
 87 | | from .render import (
 88 | |     RenderControlRequest,
 89 | |     RenderControlResponse,
 90 | |     RenderControlType,
 91 | |     RenderInitRequest,
 92 | |     RenderInitResponse,
 93 | |     RenderStatus,
 94 | |     RenderStatusResponse,
 95 | | )
 96 | | from .token import (
 97 | |     RefreshTokenRequest,
 98 | |     Token,
 99 | |     TokenPayload,
100 | |     TokenResponse,
101 | | )
102 | | from .tribute import (
103 | |     ClientInfo,
104 | |     TributeBase,
105 | |     TributeCreateRequest,
106 | |     TributeItem,
107 | |     TributeListResponse,
108 | |     TributeResponse,
109 | |     TributeStatsResponse,
110 | | )
111 | | from .user import (
112 | |     UserBase,
113 | |     UserCreate,
114 | |     UserInDBBase,
115 | |     UserResponse,
116 | |     UserUpdate,
117 | |     UserUpdateBase,
118 | | )
    | |_^ I001
119 |
120 |   # Re-export BaseModel so it's available as schemas.BaseModel
    |
    = help: Organize imports

app/schemas_pydantic/__init__.py:57:5: F401 `.memorial_space.MemorialCustomSettings` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
   |
55 | from .memorial_space import (
56 |     DeceasedGenderEnum,
57 |     MemorialCustomSettings,
   |     ^^^^^^^^^^^^^^^^^^^^^^ F401
58 |     MemorialSpaceBase,
59 |     MemorialSpaceCreate,
   |
   = help: Add unused import `MemorialCustomSettings` to __all__

app/schemas_pydantic/__init__.py:62:5: F401 `.memorial_space.SceneCustomization` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
   |
60 |     MemorialSpaceListResponse,  # Added import
61 |     MemorialSpaceResponse,
62 |     SceneCustomization,
   |     ^^^^^^^^^^^^^^^^^^ F401
63 |     MemorialSpaceUpdate,
64 |     PrivacyLevelEnum,
   |
   = help: Add unused import `SceneCustomization` to __all__

app/schemas_pydantic/__init__.py:67:5: F401 `.permission.AccessCheckRequest` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
   |
65 | )
66 | from .permission import (
67 |     AccessCheckRequest,
   |     ^^^^^^^^^^^^^^^^^^ F401
68 |     AccessCheckResponse,
69 |     AccessLogEntry,
   |
   = help: Add unused import `AccessCheckRequest` to __all__

app/schemas_pydantic/__init__.py:68:5: F401 `.permission.AccessCheckResponse` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
   |
66 | from .permission import (
67 |     AccessCheckRequest,
68 |     AccessCheckResponse,
   |     ^^^^^^^^^^^^^^^^^^^ F401
69 |     AccessLogEntry,
70 |     AccessLogListResponse,
   |
   = help: Add unused import `AccessCheckResponse` to __all__

app/schemas_pydantic/__init__.py:69:5: F401 `.permission.AccessLogEntry` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
   |
67 |     AccessCheckRequest,
68 |     AccessCheckResponse,
69 |     AccessLogEntry,
   |     ^^^^^^^^^^^^^^ F401
70 |     AccessLogListResponse,
71 |     AccessType,
   |
   = help: Add unused import `AccessLogEntry` to __all__

app/schemas_pydantic/__init__.py:70:5: F401 `.permission.AccessLogListResponse` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
   |
68 |     AccessCheckResponse,
69 |     AccessLogEntry,
70 |     AccessLogListResponse,
   |     ^^^^^^^^^^^^^^^^^^^^^ F401
71 |     AccessType,
72 |     FamilyAccessRequest,
   |
   = help: Add unused import `AccessLogListResponse` to __all__

app/schemas_pydantic/__init__.py:71:5: F401 `.permission.AccessType` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
   |
69 |     AccessLogEntry,
70 |     AccessLogListResponse,
71 |     AccessType,
   |     ^^^^^^^^^^ F401
72 |     FamilyAccessRequest,
73 |     PermissionBase,
   |
   = help: Add unused import `AccessType` to __all__

app/schemas_pydantic/__init__.py:72:5: F401 `.permission.FamilyAccessRequest` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
   |
70 |     AccessLogListResponse,
71 |     AccessType,
72 |     FamilyAccessRequest,
   |     ^^^^^^^^^^^^^^^^^^^ F401
73 |     PermissionBase,
74 |     PermissionBatchOperation,
   |
   = help: Add unused import `FamilyAccessRequest` to __all__

app/schemas_pydantic/__init__.py:73:5: F401 `.permission.PermissionBase` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
   |
71 |     AccessType,
72 |     FamilyAccessRequest,
73 |     PermissionBase,
   |     ^^^^^^^^^^^^^^ F401
74 |     PermissionBatchOperation,
75 |     PermissionCreate,
   |
   = help: Add unused import `PermissionBase` to __all__

app/schemas_pydantic/__init__.py:74:5: F401 `.permission.PermissionBatchOperation` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
   |
72 |     FamilyAccessRequest,
73 |     PermissionBase,
74 |     PermissionBatchOperation,
   |     ^^^^^^^^^^^^^^^^^^^^^^^^ F401
75 |     PermissionCreate,
76 |     PermissionResponse,
   |
   = help: Add unused import `PermissionBatchOperation` to __all__

app/schemas_pydantic/__init__.py:75:5: F401 `.permission.PermissionCreate` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
   |
73 |     PermissionBase,
74 |     PermissionBatchOperation,
75 |     PermissionCreate,
   |     ^^^^^^^^^^^^^^^^ F401
76 |     PermissionResponse,
77 |     PermissionType,
   |
   = help: Add unused import `PermissionCreate` to __all__

app/schemas_pydantic/__init__.py:76:5: F401 `.permission.PermissionResponse` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
   |
74 |     PermissionBatchOperation,
75 |     PermissionCreate,
76 |     PermissionResponse,
   |     ^^^^^^^^^^^^^^^^^^ F401
77 |     PermissionType,
78 |     PermissionUpdate,
   |
   = help: Add unused import `PermissionResponse` to __all__

app/schemas_pydantic/__init__.py:77:5: F401 `.permission.PermissionType` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
   |
75 |     PermissionCreate,
76 |     PermissionResponse,
77 |     PermissionType,
   |     ^^^^^^^^^^^^^^ F401
78 |     PermissionUpdate,
79 |     PrivacySettings,
   |
   = help: Add unused import `PermissionType` to __all__

app/schemas_pydantic/__init__.py:78:5: F401 `.permission.PermissionUpdate` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
   |
76 |     PermissionResponse,
77 |     PermissionType,
78 |     PermissionUpdate,
   |     ^^^^^^^^^^^^^^^^ F401
79 |     PrivacySettings,
80 | )
   |
   = help: Add unused import `PermissionUpdate` to __all__

app/schemas_pydantic/__init__.py:79:5: F401 `.permission.PrivacySettings` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
   |
77 |     PermissionType,
78 |     PermissionUpdate,
79 |     PrivacySettings,
   |     ^^^^^^^^^^^^^^^ F401
80 | )
81 | from .religious_setting import (
   |
   = help: Add unused import `PrivacySettings` to __all__

app/schemas_pydantic/__init__.py:103:5: F401 `.tribute.ClientInfo` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
    |
101 | )
102 | from .tribute import (
103 |     ClientInfo,
    |     ^^^^^^^^^^ F401
104 |     TributeBase,
105 |     TributeCreateRequest,
    |
    = help: Add unused import `ClientInfo` to __all__

app/schemas_pydantic/__init__.py:106:5: F401 `.tribute.TributeItem` imported but unused; consider removing, adding to `__all__`, or using a redundant alias
    |
104 |     TributeBase,
105 |     TributeCreateRequest,
106 |     TributeItem,
    |     ^^^^^^^^^^^ F401
107 |     TributeListResponse,
108 |     TributeResponse,
    |
    = help: Add unused import `TributeItem` to __all__

app/schemas_pydantic/membership.py:1:1: UP035 `typing.Dict` is deprecated, use `dict` instead
  |
1 | from typing import Dict, List, Optional, Any, Union
  | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP035
2 | from pydantic import BaseModel, Field
3 | from datetime import datetime
  |

app/schemas_pydantic/membership.py:1:1: UP035 `typing.List` is deprecated, use `list` instead
  |
1 | from typing import Dict, List, Optional, Any, Union
  | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP035
2 | from pydantic import BaseModel, Field
3 | from datetime import datetime
  |

app/schemas_pydantic/membership.py:1:1: I001 [*] Import block is un-sorted or un-formatted
  |
1 | / from typing import Dict, List, Optional, Any, Union
2 | | from pydantic import BaseModel, Field
3 | | from datetime import datetime
  | |_____________________________^ I001
  |
  = help: Organize imports

app/schemas_pydantic/membership.py:3:22: F401 [*] `datetime.datetime` imported but unused
  |
1 | from typing import Dict, List, Optional, Any, Union
2 | from pydantic import BaseModel, Field
3 | from datetime import datetime
  |                      ^^^^^^^^ F401
  |
  = help: Remove unused import: `datetime.datetime`

app/schemas_pydantic/membership.py:10:16: UP007 [*] Use `X | Y` for type annotations
   |
 9 |     allowed: bool = Field(..., description="是否允许访问")
10 |     remaining: Optional[Union[int, float]] = Field(None, description="剩余可用量")
   |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP007
11 |     limit: Union[int, float, bool] = Field(..., description="功能限制")
12 |     current_usage: Optional[Union[int, float]] = Field(None, description="当前使用量")
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/membership.py:10:25: UP007 [*] Use `X | Y` for type annotations
   |
 9 |     allowed: bool = Field(..., description="是否允许访问")
10 |     remaining: Optional[Union[int, float]] = Field(None, description="剩余可用量")
   |                         ^^^^^^^^^^^^^^^^^ UP007
11 |     limit: Union[int, float, bool] = Field(..., description="功能限制")
12 |     current_usage: Optional[Union[int, float]] = Field(None, description="当前使用量")
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/membership.py:11:12: UP007 [*] Use `X | Y` for type annotations
   |
 9 |     allowed: bool = Field(..., description="是否允许访问")
10 |     remaining: Optional[Union[int, float]] = Field(None, description="剩余可用量")
11 |     limit: Union[int, float, bool] = Field(..., description="功能限制")
   |            ^^^^^^^^^^^^^^^^^^^^^^^ UP007
12 |     current_usage: Optional[Union[int, float]] = Field(None, description="当前使用量")
13 |     tier: str = Field(..., description="用户会员等级")
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/membership.py:12:20: UP007 [*] Use `X | Y` for type annotations
   |
10 |     remaining: Optional[Union[int, float]] = Field(None, description="剩余可用量")
11 |     limit: Union[int, float, bool] = Field(..., description="功能限制")
12 |     current_usage: Optional[Union[int, float]] = Field(None, description="当前使用量")
   |                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP007
13 |     tier: str = Field(..., description="用户会员等级")
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/membership.py:12:29: UP007 [*] Use `X | Y` for type annotations
   |
10 |     remaining: Optional[Union[int, float]] = Field(None, description="剩余可用量")
11 |     limit: Union[int, float, bool] = Field(..., description="功能限制")
12 |     current_usage: Optional[Union[int, float]] = Field(None, description="当前使用量")
   |                             ^^^^^^^^^^^^^^^^^ UP007
13 |     tier: str = Field(..., description="用户会员等级")
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/membership.py:20:16: UP007 [*] Use `X | Y` for type annotations
   |
19 |     allowed: bool = Field(..., description="是否允许操作")
20 |     remaining: Optional[Union[int, float]] = Field(None, description="剩余可用量")
   |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP007
21 |     limit: Optional[Union[int, float, bool]] = Field(None, description="功能限制")
22 |     current_usage: Optional[Union[int, float]] = Field(None, description="当前使用量")
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/membership.py:20:25: UP007 [*] Use `X | Y` for type annotations
   |
19 |     allowed: bool = Field(..., description="是否允许操作")
20 |     remaining: Optional[Union[int, float]] = Field(None, description="剩余可用量")
   |                         ^^^^^^^^^^^^^^^^^ UP007
21 |     limit: Optional[Union[int, float, bool]] = Field(None, description="功能限制")
22 |     current_usage: Optional[Union[int, float]] = Field(None, description="当前使用量")
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/membership.py:21:12: UP007 [*] Use `X | Y` for type annotations
   |
19 |     allowed: bool = Field(..., description="是否允许操作")
20 |     remaining: Optional[Union[int, float]] = Field(None, description="剩余可用量")
21 |     limit: Optional[Union[int, float, bool]] = Field(None, description="功能限制")
   |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP007
22 |     current_usage: Optional[Union[int, float]] = Field(None, description="当前使用量")
23 |     tier: Optional[str] = Field(None, description="用户会员等级")
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/membership.py:21:21: UP007 [*] Use `X | Y` for type annotations
   |
19 |     allowed: bool = Field(..., description="是否允许操作")
20 |     remaining: Optional[Union[int, float]] = Field(None, description="剩余可用量")
21 |     limit: Optional[Union[int, float, bool]] = Field(None, description="功能限制")
   |                     ^^^^^^^^^^^^^^^^^^^^^^^ UP007
22 |     current_usage: Optional[Union[int, float]] = Field(None, description="当前使用量")
23 |     tier: Optional[str] = Field(None, description="用户会员等级")
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/membership.py:22:20: UP007 [*] Use `X | Y` for type annotations
   |
20 |     remaining: Optional[Union[int, float]] = Field(None, description="剩余可用量")
21 |     limit: Optional[Union[int, float, bool]] = Field(None, description="功能限制")
22 |     current_usage: Optional[Union[int, float]] = Field(None, description="当前使用量")
   |                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP007
23 |     tier: Optional[str] = Field(None, description="用户会员等级")
24 |     error: Optional[str] = Field(None, description="错误信息")
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/membership.py:22:29: UP007 [*] Use `X | Y` for type annotations
   |
20 |     remaining: Optional[Union[int, float]] = Field(None, description="剩余可用量")
21 |     limit: Optional[Union[int, float, bool]] = Field(None, description="功能限制")
22 |     current_usage: Optional[Union[int, float]] = Field(None, description="当前使用量")
   |                             ^^^^^^^^^^^^^^^^^ UP007
23 |     tier: Optional[str] = Field(None, description="用户会员等级")
24 |     error: Optional[str] = Field(None, description="错误信息")
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/membership.py:23:11: UP007 [*] Use `X | Y` for type annotations
   |
21 |     limit: Optional[Union[int, float, bool]] = Field(None, description="功能限制")
22 |     current_usage: Optional[Union[int, float]] = Field(None, description="当前使用量")
23 |     tier: Optional[str] = Field(None, description="用户会员等级")
   |           ^^^^^^^^^^^^^ UP007
24 |     error: Optional[str] = Field(None, description="错误信息")
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/membership.py:24:12: UP007 [*] Use `X | Y` for type annotations
   |
22 |     current_usage: Optional[Union[int, float]] = Field(None, description="当前使用量")
23 |     tier: Optional[str] = Field(None, description="用户会员等级")
24 |     error: Optional[str] = Field(None, description="错误信息")
   |            ^^^^^^^^^^^^^ UP007
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/membership.py:31:13: UP007 [*] Use `X | Y` for type annotations
   |
30 |     feature_type: str = Field(..., description="功能类型")
31 |     amount: Union[int, float] = Field(1, description="使用量", gt=0)
   |             ^^^^^^^^^^^^^^^^^ UP007
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/membership.py:49:22: UP007 [*] Use `X | Y` for type annotations
   |
47 |     """等级限制"""
48 |
49 |     memorial_spaces: Union[int, str] = Field(..., description="纪念空间数量限制")
   |                      ^^^^^^^^^^^^^^^ UP007
50 |     storage_gb: Union[int, str] = Field(..., description="存储空间限制(GB)")
51 |     ai_minutes: Union[int, str] = Field(..., description="AI服务分钟数限制")
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/membership.py:50:17: UP007 [*] Use `X | Y` for type annotations
   |
49 |     memorial_spaces: Union[int, str] = Field(..., description="纪念空间数量限制")
50 |     storage_gb: Union[int, str] = Field(..., description="存储空间限制(GB)")
   |                 ^^^^^^^^^^^^^^^ UP007
51 |     ai_minutes: Union[int, str] = Field(..., description="AI服务分钟数限制")
52 |     custom_domains: Union[int, str] = Field(..., description="自定义域名数量限制")
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/membership.py:51:17: UP007 [*] Use `X | Y` for type annotations
   |
49 |     memorial_spaces: Union[int, str] = Field(..., description="纪念空间数量限制")
50 |     storage_gb: Union[int, str] = Field(..., description="存储空间限制(GB)")
51 |     ai_minutes: Union[int, str] = Field(..., description="AI服务分钟数限制")
   |                 ^^^^^^^^^^^^^^^ UP007
52 |     custom_domains: Union[int, str] = Field(..., description="自定义域名数量限制")
53 |     api_calls: Union[int, str] = Field(..., description="API调用次数限制")
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/membership.py:52:21: UP007 [*] Use `X | Y` for type annotations
   |
50 |     storage_gb: Union[int, str] = Field(..., description="存储空间限制(GB)")
51 |     ai_minutes: Union[int, str] = Field(..., description="AI服务分钟数限制")
52 |     custom_domains: Union[int, str] = Field(..., description="自定义域名数量限制")
   |                     ^^^^^^^^^^^^^^^ UP007
53 |     api_calls: Union[int, str] = Field(..., description="API调用次数限制")
54 |     priority_support: bool = Field(..., description="是否有优先支持")
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/membership.py:53:16: UP007 [*] Use `X | Y` for type annotations
   |
51 |     ai_minutes: Union[int, str] = Field(..., description="AI服务分钟数限制")
52 |     custom_domains: Union[int, str] = Field(..., description="自定义域名数量限制")
53 |     api_calls: Union[int, str] = Field(..., description="API调用次数限制")
   |                ^^^^^^^^^^^^^^^ UP007
54 |     priority_support: bool = Field(..., description="是否有优先支持")
55 |     advanced_analytics: bool = Field(..., description="是否有高级分析")
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/membership.py:58:23: UP007 [*] Use `X | Y` for type annotations
   |
56 |     white_label: bool = Field(..., description="是否有白标服务")
57 |     custom_branding: bool = Field(..., description="是否有自定义品牌")
58 |     backup_retention: Union[int, str] = Field(..., description="备份保留天数")
   |                       ^^^^^^^^^^^^^^^ UP007
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/membership.py:65:20: UP007 [*] Use `X | Y` for type annotations
   |
64 |     feature: str = Field(..., description="功能名称")
65 |     current_usage: Union[int, float] = Field(..., description="当前使用量")
   |                    ^^^^^^^^^^^^^^^^^ UP007
66 |     current_limit: Union[int, float] = Field(..., description="当前限制")
67 |     usage_percentage: float = Field(..., description="使用百分比")
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/membership.py:66:20: UP007 [*] Use `X | Y` for type annotations
   |
64 |     feature: str = Field(..., description="功能名称")
65 |     current_usage: Union[int, float] = Field(..., description="当前使用量")
66 |     current_limit: Union[int, float] = Field(..., description="当前限制")
   |                    ^^^^^^^^^^^^^^^^^ UP007
67 |     usage_percentage: float = Field(..., description="使用百分比")
68 |     recommended_tier: str = Field(..., description="推荐的等级")
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/membership.py:88:13: UP006 [*] Use `dict` instead of `Dict` for type annotation
   |
86 |     current_tier: str = Field(..., description="当前会员等级")
87 |     tier_display_name: str = Field(..., description="等级显示名称")
88 |     limits: Dict[str, Any] = Field(..., description="功能限制")
   |             ^^^^ UP006
89 |     usage: Dict[str, Any] = Field(..., description="使用情况")
90 |     usage_percentages: Dict[str, Optional[float]] = Field(..., description="使用百分比")
   |
   = help: Replace with `dict`

app/schemas_pydantic/membership.py:89:12: UP006 [*] Use `dict` instead of `Dict` for type annotation
   |
87 |     tier_display_name: str = Field(..., description="等级显示名称")
88 |     limits: Dict[str, Any] = Field(..., description="功能限制")
89 |     usage: Dict[str, Any] = Field(..., description="使用情况")
   |            ^^^^ UP006
90 |     usage_percentages: Dict[str, Optional[float]] = Field(..., description="使用百分比")
91 |     upgrade_recommendations: List[UpgradeRecommendation] = Field(
   |
   = help: Replace with `dict`

app/schemas_pydantic/membership.py:90:24: UP006 [*] Use `dict` instead of `Dict` for type annotation
   |
88 |     limits: Dict[str, Any] = Field(..., description="功能限制")
89 |     usage: Dict[str, Any] = Field(..., description="使用情况")
90 |     usage_percentages: Dict[str, Optional[float]] = Field(..., description="使用百分比")
   |                        ^^^^ UP006
91 |     upgrade_recommendations: List[UpgradeRecommendation] = Field(
92 |         ..., description="升级建议"
   |
   = help: Replace with `dict`

app/schemas_pydantic/membership.py:90:34: UP007 [*] Use `X | Y` for type annotations
   |
88 |     limits: Dict[str, Any] = Field(..., description="功能限制")
89 |     usage: Dict[str, Any] = Field(..., description="使用情况")
90 |     usage_percentages: Dict[str, Optional[float]] = Field(..., description="使用百分比")
   |                                  ^^^^^^^^^^^^^^^ UP007
91 |     upgrade_recommendations: List[UpgradeRecommendation] = Field(
92 |         ..., description="升级建议"
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/membership.py:91:30: UP006 [*] Use `list` instead of `List` for type annotation
   |
89 |     usage: Dict[str, Any] = Field(..., description="使用情况")
90 |     usage_percentages: Dict[str, Optional[float]] = Field(..., description="使用百分比")
91 |     upgrade_recommendations: List[UpgradeRecommendation] = Field(
   |                              ^^^^ UP006
92 |         ..., description="升级建议"
93 |     )
   |
   = help: Replace with `list`

app/schemas_pydantic/membership.py:103:13: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
101 |     name: str = Field(..., description="等级名称")
102 |     description: str = Field(..., description="等级描述")
103 |     limits: Dict[str, Any] = Field(..., description="功能限制")
    |             ^^^^ UP006
    |
    = help: Replace with `dict`

app/schemas_pydantic/membership.py:109:12: UP006 [*] Use `list` instead of `List` for type annotation
    |
107 |     """所有等级信息响应"""
108 |
109 |     tiers: List[TierInfo] = Field(..., description="所有等级信息")
    |            ^^^^ UP006
    |
    = help: Replace with `list`

app/schemas_pydantic/membership.py:116:20: UP007 [*] Use `X | Y` for type annotations
    |
114 |     """会员权益要求"""
115 |
116 |     required_tier: Optional[str] = Field(None, description="要求的最低等级")
    |                    ^^^^^^^^^^^^^ UP007
117 |     required_features: Optional[List[str]] = Field(None, description="要求的功能")
118 |     feature_usage: Optional[Dict[str, Union[int, float]]] = Field(
    |
    = help: Convert to `X | Y`

app/schemas_pydantic/membership.py:117:24: UP007 [*] Use `X | Y` for type annotations
    |
116 |     required_tier: Optional[str] = Field(None, description="要求的最低等级")
117 |     required_features: Optional[List[str]] = Field(None, description="要求的功能")
    |                        ^^^^^^^^^^^^^^^^^^^ UP007
118 |     feature_usage: Optional[Dict[str, Union[int, float]]] = Field(
119 |         None, description="功能使用量要求"
    |
    = help: Convert to `X | Y`

app/schemas_pydantic/membership.py:117:33: UP006 [*] Use `list` instead of `List` for type annotation
    |
116 |     required_tier: Optional[str] = Field(None, description="要求的最低等级")
117 |     required_features: Optional[List[str]] = Field(None, description="要求的功能")
    |                                 ^^^^ UP006
118 |     feature_usage: Optional[Dict[str, Union[int, float]]] = Field(
119 |         None, description="功能使用量要求"
    |
    = help: Replace with `list`

app/schemas_pydantic/membership.py:118:20: UP007 [*] Use `X | Y` for type annotations
    |
116 |     required_tier: Optional[str] = Field(None, description="要求的最低等级")
117 |     required_features: Optional[List[str]] = Field(None, description="要求的功能")
118 |     feature_usage: Optional[Dict[str, Union[int, float]]] = Field(
    |                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP007
119 |         None, description="功能使用量要求"
120 |     )
    |
    = help: Convert to `X | Y`

app/schemas_pydantic/membership.py:118:29: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
116 |     required_tier: Optional[str] = Field(None, description="要求的最低等级")
117 |     required_features: Optional[List[str]] = Field(None, description="要求的功能")
118 |     feature_usage: Optional[Dict[str, Union[int, float]]] = Field(
    |                             ^^^^ UP006
119 |         None, description="功能使用量要求"
120 |     )
    |
    = help: Replace with `dict`

app/schemas_pydantic/membership.py:118:39: UP007 [*] Use `X | Y` for type annotations
    |
116 |     required_tier: Optional[str] = Field(None, description="要求的最低等级")
117 |     required_features: Optional[List[str]] = Field(None, description="要求的功能")
118 |     feature_usage: Optional[Dict[str, Union[int, float]]] = Field(
    |                                       ^^^^^^^^^^^^^^^^^ UP007
119 |         None, description="功能使用量要求"
120 |     )
    |
    = help: Convert to `X | Y`

app/schemas_pydantic/membership.py:128:20: UP007 [*] Use `X | Y` for type annotations
    |
126 |     valid: bool = Field(..., description="是否通过验证")
127 |     user_tier: str = Field(..., description="用户当前等级")
128 |     required_tier: Optional[str] = Field(None, description="要求的等级")
    |                    ^^^^^^^^^^^^^ UP007
129 |     missing_features: Optional[List[str]] = Field(None, description="缺少的功能")
130 |     insufficient_usage: Optional[Dict[str, Any]] = Field(None, description="使用量不足的功能")
    |
    = help: Convert to `X | Y`

app/schemas_pydantic/membership.py:129:23: UP007 [*] Use `X | Y` for type annotations
    |
127 |     user_tier: str = Field(..., description="用户当前等级")
128 |     required_tier: Optional[str] = Field(None, description="要求的等级")
129 |     missing_features: Optional[List[str]] = Field(None, description="缺少的功能")
    |                       ^^^^^^^^^^^^^^^^^^^ UP007
130 |     insufficient_usage: Optional[Dict[str, Any]] = Field(None, description="使用量不足的功能")
131 |     error_message: Optional[str] = Field(None, description="错误信息")
    |
    = help: Convert to `X | Y`

app/schemas_pydantic/membership.py:129:32: UP006 [*] Use `list` instead of `List` for type annotation
    |
127 |     user_tier: str = Field(..., description="用户当前等级")
128 |     required_tier: Optional[str] = Field(None, description="要求的等级")
129 |     missing_features: Optional[List[str]] = Field(None, description="缺少的功能")
    |                                ^^^^ UP006
130 |     insufficient_usage: Optional[Dict[str, Any]] = Field(None, description="使用量不足的功能")
131 |     error_message: Optional[str] = Field(None, description="错误信息")
    |
    = help: Replace with `list`

app/schemas_pydantic/membership.py:130:25: UP007 [*] Use `X | Y` for type annotations
    |
128 |     required_tier: Optional[str] = Field(None, description="要求的等级")
129 |     missing_features: Optional[List[str]] = Field(None, description="缺少的功能")
130 |     insufficient_usage: Optional[Dict[str, Any]] = Field(None, description="使用量不足的功能")
    |                         ^^^^^^^^^^^^^^^^^^^^^^^^ UP007
131 |     error_message: Optional[str] = Field(None, description="错误信息")
132 |     upgrade_suggestion: Optional[str] = Field(None, description="升级建议")
    |
    = help: Convert to `X | Y`

app/schemas_pydantic/membership.py:130:34: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
128 |     required_tier: Optional[str] = Field(None, description="要求的等级")
129 |     missing_features: Optional[List[str]] = Field(None, description="缺少的功能")
130 |     insufficient_usage: Optional[Dict[str, Any]] = Field(None, description="使用量不足的功能")
    |                                  ^^^^ UP006
131 |     error_message: Optional[str] = Field(None, description="错误信息")
132 |     upgrade_suggestion: Optional[str] = Field(None, description="升级建议")
    |
    = help: Replace with `dict`

app/schemas_pydantic/membership.py:131:20: UP007 [*] Use `X | Y` for type annotations
    |
129 |     missing_features: Optional[List[str]] = Field(None, description="缺少的功能")
130 |     insufficient_usage: Optional[Dict[str, Any]] = Field(None, description="使用量不足的功能")
131 |     error_message: Optional[str] = Field(None, description="错误信息")
    |                    ^^^^^^^^^^^^^ UP007
132 |     upgrade_suggestion: Optional[str] = Field(None, description="升级建议")
    |
    = help: Convert to `X | Y`

app/schemas_pydantic/membership.py:132:25: UP007 [*] Use `X | Y` for type annotations
    |
130 |     insufficient_usage: Optional[Dict[str, Any]] = Field(None, description="使用量不足的功能")
131 |     error_message: Optional[str] = Field(None, description="错误信息")
132 |     upgrade_suggestion: Optional[str] = Field(None, description="升级建议")
    |                         ^^^^^^^^^^^^^ UP007
    |
    = help: Convert to `X | Y`

app/schemas_pydantic/membership.py:140:20: UP007 [*] Use `X | Y` for type annotations
    |
139 |     feature_type: str = Field(..., description="功能类型")
140 |     current_usage: Union[int, float] = Field(..., description="当前使用量")
    |                    ^^^^^^^^^^^^^^^^^ UP007
141 |     limit: Union[int, float] = Field(..., description="限制")
142 |     usage_percentage: float = Field(..., description="使用百分比")
    |
    = help: Convert to `X | Y`

app/schemas_pydantic/membership.py:141:12: UP007 [*] Use `X | Y` for type annotations
    |
139 |     feature_type: str = Field(..., description="功能类型")
140 |     current_usage: Union[int, float] = Field(..., description="当前使用量")
141 |     limit: Union[int, float] = Field(..., description="限制")
    |            ^^^^^^^^^^^^^^^^^ UP007
142 |     usage_percentage: float = Field(..., description="使用百分比")
143 |     alert_level: str = Field(..., description="警告级别: warning/critical")
    |
    = help: Convert to `X | Y`

app/schemas_pydantic/membership.py:152:13: UP006 [*] Use `list` instead of `List` for type annotation
    |
150 |     user_id: str = Field(..., description="用户ID")
151 |     tier: str = Field(..., description="用户等级")
152 |     alerts: List[UsageAlert] = Field(..., description="使用量警告")
    |             ^^^^ UP006
153 |     recommendations: List[UpgradeRecommendation] = Field(..., description="升级建议")
154 |     overall_status: str = Field(..., description="整体状态: healthy/warning/critical")
    |
    = help: Replace with `list`

app/schemas_pydantic/membership.py:153:22: UP006 [*] Use `list` instead of `List` for type annotation
    |
151 |     tier: str = Field(..., description="用户等级")
152 |     alerts: List[UsageAlert] = Field(..., description="使用量警告")
153 |     recommendations: List[UpgradeRecommendation] = Field(..., description="升级建议")
    |                      ^^^^ UP006
154 |     overall_status: str = Field(..., description="整体状态: healthy/warning/critical")
    |
    = help: Replace with `list`

app/schemas_pydantic/membership.py:163:19: UP007 [*] Use `X | Y` for type annotations
    |
161 |     feature_type: str = Field(..., description="功能类型")
162 |     date: str = Field(..., description="日期")
163 |     usage_amount: Union[int, float] = Field(..., description="使用量")
    |                   ^^^^^^^^^^^^^^^^^ UP007
164 |     cumulative_usage: Union[int, float] = Field(..., description="累计使用量")
    |
    = help: Convert to `X | Y`

app/schemas_pydantic/membership.py:164:23: UP007 [*] Use `X | Y` for type annotations
    |
162 |     date: str = Field(..., description="日期")
163 |     usage_amount: Union[int, float] = Field(..., description="使用量")
164 |     cumulative_usage: Union[int, float] = Field(..., description="累计使用量")
    |                       ^^^^^^^^^^^^^^^^^ UP007
    |
    = help: Convert to `X | Y`

app/schemas_pydantic/membership.py:173:14: UP006 [*] Use `list` instead of `List` for type annotation
    |
171 |     period_start: str = Field(..., description="统计开始日期")
172 |     period_end: str = Field(..., description="统计结束日期")
173 |     history: List[FeatureUsageHistory] = Field(..., description="使用历史")
    |              ^^^^ UP006
    |
    = help: Replace with `list`

app/schemas_pydantic/membership.py:181:11: UP007 [*] Use `X | Y` for type annotations
    |
180 |     feature_name: str = Field(..., description="功能名称")
181 |     free: Union[int, float, bool, str] = Field(..., description="免费版")
    |           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP007
182 |     premium: Union[int, float, bool, str] = Field(..., description="高级版")
183 |     family: Union[int, float, bool, str] = Field(..., description="家族版")
    |
    = help: Convert to `X | Y`

app/schemas_pydantic/membership.py:182:14: UP007 [*] Use `X | Y` for type annotations
    |
180 |     feature_name: str = Field(..., description="功能名称")
181 |     free: Union[int, float, bool, str] = Field(..., description="免费版")
182 |     premium: Union[int, float, bool, str] = Field(..., description="高级版")
    |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP007
183 |     family: Union[int, float, bool, str] = Field(..., description="家族版")
184 |     enterprise: Union[int, float, bool, str] = Field(..., description="企业版")
    |
    = help: Convert to `X | Y`

app/schemas_pydantic/membership.py:183:13: UP007 [*] Use `X | Y` for type annotations
    |
181 |     free: Union[int, float, bool, str] = Field(..., description="免费版")
182 |     premium: Union[int, float, bool, str] = Field(..., description="高级版")
183 |     family: Union[int, float, bool, str] = Field(..., description="家族版")
    |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP007
184 |     enterprise: Union[int, float, bool, str] = Field(..., description="企业版")
    |
    = help: Convert to `X | Y`

app/schemas_pydantic/membership.py:184:17: UP007 [*] Use `X | Y` for type annotations
    |
182 |     premium: Union[int, float, bool, str] = Field(..., description="高级版")
183 |     family: Union[int, float, bool, str] = Field(..., description="家族版")
184 |     enterprise: Union[int, float, bool, str] = Field(..., description="企业版")
    |                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP007
    |
    = help: Convert to `X | Y`

app/schemas_pydantic/membership.py:190:18: UP006 [*] Use `list` instead of `List` for type annotation
    |
188 |     """等级比较响应"""
189 |
190 |     comparisons: List[TierComparison] = Field(..., description="功能比较")
    |                  ^^^^ UP006
191 |     recommendations: Dict[str, str] = Field(..., description="推荐理由")
    |
    = help: Replace with `list`

app/schemas_pydantic/membership.py:191:22: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
190 |     comparisons: List[TierComparison] = Field(..., description="功能比较")
191 |     recommendations: Dict[str, str] = Field(..., description="推荐理由")
    |                      ^^^^ UP006
    |
    = help: Replace with `dict`

app/schemas_pydantic/permission.py:33:17: UP007 [*] Use `X | Y` for type annotations
   |
32 |     permission_type: PermissionType
33 |     expires_at: Optional[datetime] = Field(None, description="权限过期时间")
   |                 ^^^^^^^^^^^^^^^^^^ UP007
34 |     can_view_private_info: bool = Field(default=False, description="能否查看私密信息")
35 |     can_moderate: bool = Field(default=False, description="能否审核内容")
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/permission.py:49:22: UP007 [*] Use `X | Y` for type annotations
   |
47 |     """更新权限请求"""
48 |
49 |     permission_type: Optional[PermissionType] = None
   |                      ^^^^^^^^^^^^^^^^^^^^^^^^ UP007
50 |     expires_at: Optional[datetime] = None
51 |     can_view_private_info: Optional[bool] = None
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/permission.py:50:17: UP007 [*] Use `X | Y` for type annotations
   |
49 |     permission_type: Optional[PermissionType] = None
50 |     expires_at: Optional[datetime] = None
   |                 ^^^^^^^^^^^^^^^^^^ UP007
51 |     can_view_private_info: Optional[bool] = None
52 |     can_moderate: Optional[bool] = None
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/permission.py:51:28: UP007 [*] Use `X | Y` for type annotations
   |
49 |     permission_type: Optional[PermissionType] = None
50 |     expires_at: Optional[datetime] = None
51 |     can_view_private_info: Optional[bool] = None
   |                            ^^^^^^^^^^^^^^ UP007
52 |     can_moderate: Optional[bool] = None
53 |     can_invite_others: Optional[bool] = None
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/permission.py:52:19: UP007 [*] Use `X | Y` for type annotations
   |
50 |     expires_at: Optional[datetime] = None
51 |     can_view_private_info: Optional[bool] = None
52 |     can_moderate: Optional[bool] = None
   |                   ^^^^^^^^^^^^^^ UP007
53 |     can_invite_others: Optional[bool] = None
54 |     is_active: Optional[bool] = None
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/permission.py:53:24: UP007 [*] Use `X | Y` for type annotations
   |
51 |     can_view_private_info: Optional[bool] = None
52 |     can_moderate: Optional[bool] = None
53 |     can_invite_others: Optional[bool] = None
   |                        ^^^^^^^^^^^^^^ UP007
54 |     is_active: Optional[bool] = None
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/permission.py:54:16: UP007 [*] Use `X | Y` for type annotations
   |
52 |     can_moderate: Optional[bool] = None
53 |     can_invite_others: Optional[bool] = None
54 |     is_active: Optional[bool] = None
   |                ^^^^^^^^^^^^^^ UP007
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/permission.py:66:16: UP007 [*] Use `X | Y` for type annotations
   |
64 |     granted_at: datetime
65 |     is_active: bool
66 |     user_name: Optional[str] = None
   |                ^^^^^^^^^^^^^ UP007
67 |     grantor_name: Optional[str] = None
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/permission.py:67:19: UP007 [*] Use `X | Y` for type annotations
   |
65 |     is_active: bool
66 |     user_name: Optional[str] = None
67 |     grantor_name: Optional[str] = None
   |                   ^^^^^^^^^^^^^ UP007
68 |
69 |     class Config:
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/permission.py:78:22: UP007 [*] Use `X | Y` for type annotations
   |
76 |     memorial_space_id: UUID
77 |     access_type: AccessType
78 |     access_password: Optional[str] = Field(None, description="访问密码")
   |                      ^^^^^^^^^^^^^ UP007
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/permission.py:85:23: UP007 [*] Use `X | Y` for type annotations
   |
84 |     access_granted: bool
85 |     permission_level: Optional[PermissionType] = None
   |                       ^^^^^^^^^^^^^^^^^^^^^^^^ UP007
86 |     denial_reason: Optional[str] = None
87 |     requires_password: bool = False
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/permission.py:86:20: UP007 [*] Use `X | Y` for type annotations
   |
84 |     access_granted: bool
85 |     permission_level: Optional[PermissionType] = None
86 |     denial_reason: Optional[str] = None
   |                    ^^^^^^^^^^^^^ UP007
87 |     requires_password: bool = False
88 |     requires_family_verification: bool = False
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/permission.py:96:14: UP007 [*] Use `X | Y` for type annotations
   |
94 |     id: UUID
95 |     memorial_space_id: UUID
96 |     user_id: Optional[UUID] = None
   |              ^^^^^^^^^^^^^^ UP007
97 |     access_type: AccessType
98 |     ip_address: Optional[str] = None
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/permission.py:98:17: UP007 [*] Use `X | Y` for type annotations
    |
 96 |     user_id: Optional[UUID] = None
 97 |     access_type: AccessType
 98 |     ip_address: Optional[str] = None
    |                 ^^^^^^^^^^^^^ UP007
 99 |     user_agent: Optional[str] = None
100 |     access_granted: bool
    |
    = help: Convert to `X | Y`

app/schemas_pydantic/permission.py:99:17: UP007 [*] Use `X | Y` for type annotations
    |
 97 |     access_type: AccessType
 98 |     ip_address: Optional[str] = None
 99 |     user_agent: Optional[str] = None
    |                 ^^^^^^^^^^^^^ UP007
100 |     access_granted: bool
101 |     denial_reason: Optional[str] = None
    |
    = help: Convert to `X | Y`

app/schemas_pydantic/permission.py:101:20: UP007 [*] Use `X | Y` for type annotations
    |
 99 |     user_agent: Optional[str] = None
100 |     access_granted: bool
101 |     denial_reason: Optional[str] = None
    |                    ^^^^^^^^^^^^^ UP007
102 |     accessed_at: datetime
103 |     session_duration: Optional[int] = None
    |
    = help: Convert to `X | Y`

app/schemas_pydantic/permission.py:103:23: UP007 [*] Use `X | Y` for type annotations
    |
101 |     denial_reason: Optional[str] = None
102 |     accessed_at: datetime
103 |     session_duration: Optional[int] = None
    |                       ^^^^^^^^^^^^^ UP007
104 |     user_name: Optional[str] = None
    |
    = help: Convert to `X | Y`

app/schemas_pydantic/permission.py:104:16: UP007 [*] Use `X | Y` for type annotations
    |
102 |     accessed_at: datetime
103 |     session_duration: Optional[int] = None
104 |     user_name: Optional[str] = None
    |                ^^^^^^^^^^^^^ UP007
105 |
106 |     class Config:
    |
    = help: Convert to `X | Y`

app/schemas_pydantic/permission.py:124:17: UP007 [*] Use `X | Y` for type annotations
    |
122 |     user_ids: list[UUID]
123 |     permission_type: PermissionType
124 |     expires_at: Optional[datetime] = None
    |                 ^^^^^^^^^^^^^^^^^^ UP007
125 |     can_view_private_info: bool = False
126 |     can_moderate: bool = False
    |
    = help: Convert to `X | Y`

app/schemas_pydantic/permission.py:136:20: UP007 [*] Use `X | Y` for type annotations
    |
134 |     relationship_to_deceased: str = Field(..., description="与逝者的关系")
135 |     verification_message: str = Field(..., description="验证信息")
136 |     contact_email: Optional[str] = None
    |                    ^^^^^^^^^^^^^ UP007
    |
    = help: Convert to `X | Y`

app/schemas_pydantic/permission.py:149:34: UP007 [*] Use `X | Y` for type annotations
    |
147 |     show_tribute_count: bool = Field(default=True, description="显示祭拜次数")
148 |     enable_access_logging: bool = Field(default=True, description="启用访问日志")
149 |     max_daily_tributes_per_user: Optional[int] = Field(None, description="每用户每日最大祭拜次数")
    |                                  ^^^^^^^^^^^^^ UP007
    |
    = help: Convert to `X | Y`

app/schemas_pydantic/scene.py:3:1: UP035 `typing.Dict` is deprecated, use `dict` instead
  |
1 | # 3D场景Pydantic模式
2 | from datetime import datetime
3 | from typing import Any, Dict, List, Optional
  | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP035
4 | from uuid import UUID
  |

app/schemas_pydantic/scene.py:3:1: UP035 `typing.List` is deprecated, use `list` instead
  |
1 | # 3D场景Pydantic模式
2 | from datetime import datetime
3 | from typing import Any, Dict, List, Optional
  | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP035
4 | from uuid import UUID
  |

app/schemas_pydantic/scene.py:13:18: UP007 [*] Use `X | Y` for type annotations
   |
12 |     name: str = Field(..., min_length=1, max_length=100, description="场景名称")
13 |     description: Optional[str] = Field(None, description="场景描述")
   |                  ^^^^^^^^^^^^^ UP007
14 |     category: str = Field(
15 |         ..., description="场景类别：buddhist, christian, traditional, modern, nature"
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/scene.py:18:19: UP007 [*] Use `X | Y` for type annotations
   |
16 |     )
17 |     model_url: str = Field(..., description="3D模型文件URL")
18 |     texture_urls: Optional[List[str]] = Field(default=None, description="贴图文件URLs")
   |                   ^^^^^^^^^^^^^^^^^^^ UP007
19 |     thumbnail_url: Optional[str] = Field(None, description="场景缩略图URL")
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/scene.py:18:28: UP006 [*] Use `list` instead of `List` for type annotation
   |
16 |     )
17 |     model_url: str = Field(..., description="3D模型文件URL")
18 |     texture_urls: Optional[List[str]] = Field(default=None, description="贴图文件URLs")
   |                            ^^^^ UP006
19 |     thumbnail_url: Optional[str] = Field(None, description="场景缩略图URL")
   |
   = help: Replace with `list`

app/schemas_pydantic/scene.py:19:20: UP007 [*] Use `X | Y` for type annotations
   |
17 |     model_url: str = Field(..., description="3D模型文件URL")
18 |     texture_urls: Optional[List[str]] = Field(default=None, description="贴图文件URLs")
19 |     thumbnail_url: Optional[str] = Field(None, description="场景缩略图URL")
   |                    ^^^^^^^^^^^^^ UP007
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/scene.py:25:22: UP007 [*] Use `X | Y` for type annotations
   |
23 |     """场景配置模式"""
24 |
25 |     lighting_config: Optional[Dict[str, Any]] = Field(default=None, description="光照配置")
   |                      ^^^^^^^^^^^^^^^^^^^^^^^^ UP007
26 |     camera_config: Optional[Dict[str, Any]] = Field(default=None, description="相机默认配置")
27 |     interaction_config: Optional[Dict[str, Any]] = Field(
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/scene.py:25:31: UP006 [*] Use `dict` instead of `Dict` for type annotation
   |
23 |     """场景配置模式"""
24 |
25 |     lighting_config: Optional[Dict[str, Any]] = Field(default=None, description="光照配置")
   |                               ^^^^ UP006
26 |     camera_config: Optional[Dict[str, Any]] = Field(default=None, description="相机默认配置")
27 |     interaction_config: Optional[Dict[str, Any]] = Field(
   |
   = help: Replace with `dict`

app/schemas_pydantic/scene.py:26:20: UP007 [*] Use `X | Y` for type annotations
   |
25 |     lighting_config: Optional[Dict[str, Any]] = Field(default=None, description="光照配置")
26 |     camera_config: Optional[Dict[str, Any]] = Field(default=None, description="相机默认配置")
   |                    ^^^^^^^^^^^^^^^^^^^^^^^^ UP007
27 |     interaction_config: Optional[Dict[str, Any]] = Field(
28 |         default=None, description="交互配置"
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/scene.py:26:29: UP006 [*] Use `dict` instead of `Dict` for type annotation
   |
25 |     lighting_config: Optional[Dict[str, Any]] = Field(default=None, description="光照配置")
26 |     camera_config: Optional[Dict[str, Any]] = Field(default=None, description="相机默认配置")
   |                             ^^^^ UP006
27 |     interaction_config: Optional[Dict[str, Any]] = Field(
28 |         default=None, description="交互配置"
   |
   = help: Replace with `dict`

app/schemas_pydantic/scene.py:27:25: UP007 [*] Use `X | Y` for type annotations
   |
25 |     lighting_config: Optional[Dict[str, Any]] = Field(default=None, description="光照配置")
26 |     camera_config: Optional[Dict[str, Any]] = Field(default=None, description="相机默认配置")
27 |     interaction_config: Optional[Dict[str, Any]] = Field(
   |                         ^^^^^^^^^^^^^^^^^^^^^^^^ UP007
28 |         default=None, description="交互配置"
29 |     )
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/scene.py:27:34: UP006 [*] Use `dict` instead of `Dict` for type annotation
   |
25 |     lighting_config: Optional[Dict[str, Any]] = Field(default=None, description="光照配置")
26 |     camera_config: Optional[Dict[str, Any]] = Field(default=None, description="相机默认配置")
27 |     interaction_config: Optional[Dict[str, Any]] = Field(
   |                                  ^^^^ UP006
28 |         default=None, description="交互配置"
29 |     )
   |
   = help: Replace with `dict`

app/schemas_pydantic/scene.py:30:19: UP007 [*] Use `X | Y` for type annotations
   |
28 |         default=None, description="交互配置"
29 |     )
30 |     audio_config: Optional[Dict[str, Any]] = Field(default=None, description="音频配置")
   |                   ^^^^^^^^^^^^^^^^^^^^^^^^ UP007
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/scene.py:30:28: UP006 [*] Use `dict` instead of `Dict` for type annotation
   |
28 |         default=None, description="交互配置"
29 |     )
30 |     audio_config: Optional[Dict[str, Any]] = Field(default=None, description="音频配置")
   |                            ^^^^ UP006
   |
   = help: Replace with `dict`

app/schemas_pydantic/scene.py:87:11: UP007 [*] Use `X | Y` for type annotations
   |
85 |     """更新场景模式"""
86 |
87 |     name: Optional[str] = Field(None, min_length=1, max_length=100)
   |           ^^^^^^^^^^^^^ UP007
88 |     description: Optional[str] = None
89 |     model_url: Optional[str] = None
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/scene.py:88:18: UP007 [*] Use `X | Y` for type annotations
   |
87 |     name: Optional[str] = Field(None, min_length=1, max_length=100)
88 |     description: Optional[str] = None
   |                  ^^^^^^^^^^^^^ UP007
89 |     model_url: Optional[str] = None
90 |     texture_urls: Optional[List[str]] = None
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/scene.py:89:16: UP007 [*] Use `X | Y` for type annotations
   |
87 |     name: Optional[str] = Field(None, min_length=1, max_length=100)
88 |     description: Optional[str] = None
89 |     model_url: Optional[str] = None
   |                ^^^^^^^^^^^^^ UP007
90 |     texture_urls: Optional[List[str]] = None
91 |     thumbnail_url: Optional[str] = None
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/scene.py:90:19: UP007 [*] Use `X | Y` for type annotations
   |
88 |     description: Optional[str] = None
89 |     model_url: Optional[str] = None
90 |     texture_urls: Optional[List[str]] = None
   |                   ^^^^^^^^^^^^^^^^^^^ UP007
91 |     thumbnail_url: Optional[str] = None
92 |     lighting_config: Optional[Dict[str, Any]] = None
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/scene.py:90:28: UP006 [*] Use `list` instead of `List` for type annotation
   |
88 |     description: Optional[str] = None
89 |     model_url: Optional[str] = None
90 |     texture_urls: Optional[List[str]] = None
   |                            ^^^^ UP006
91 |     thumbnail_url: Optional[str] = None
92 |     lighting_config: Optional[Dict[str, Any]] = None
   |
   = help: Replace with `list`

app/schemas_pydantic/scene.py:91:20: UP007 [*] Use `X | Y` for type annotations
   |
89 |     model_url: Optional[str] = None
90 |     texture_urls: Optional[List[str]] = None
91 |     thumbnail_url: Optional[str] = None
   |                    ^^^^^^^^^^^^^ UP007
92 |     lighting_config: Optional[Dict[str, Any]] = None
93 |     camera_config: Optional[Dict[str, Any]] = None
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/scene.py:92:22: UP007 [*] Use `X | Y` for type annotations
   |
90 |     texture_urls: Optional[List[str]] = None
91 |     thumbnail_url: Optional[str] = None
92 |     lighting_config: Optional[Dict[str, Any]] = None
   |                      ^^^^^^^^^^^^^^^^^^^^^^^^ UP007
93 |     camera_config: Optional[Dict[str, Any]] = None
94 |     interaction_config: Optional[Dict[str, Any]] = None
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/scene.py:92:31: UP006 [*] Use `dict` instead of `Dict` for type annotation
   |
90 |     texture_urls: Optional[List[str]] = None
91 |     thumbnail_url: Optional[str] = None
92 |     lighting_config: Optional[Dict[str, Any]] = None
   |                               ^^^^ UP006
93 |     camera_config: Optional[Dict[str, Any]] = None
94 |     interaction_config: Optional[Dict[str, Any]] = None
   |
   = help: Replace with `dict`

app/schemas_pydantic/scene.py:93:20: UP007 [*] Use `X | Y` for type annotations
   |
91 |     thumbnail_url: Optional[str] = None
92 |     lighting_config: Optional[Dict[str, Any]] = None
93 |     camera_config: Optional[Dict[str, Any]] = None
   |                    ^^^^^^^^^^^^^^^^^^^^^^^^ UP007
94 |     interaction_config: Optional[Dict[str, Any]] = None
95 |     audio_config: Optional[Dict[str, Any]] = None
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/scene.py:93:29: UP006 [*] Use `dict` instead of `Dict` for type annotation
   |
91 |     thumbnail_url: Optional[str] = None
92 |     lighting_config: Optional[Dict[str, Any]] = None
93 |     camera_config: Optional[Dict[str, Any]] = None
   |                             ^^^^ UP006
94 |     interaction_config: Optional[Dict[str, Any]] = None
95 |     audio_config: Optional[Dict[str, Any]] = None
   |
   = help: Replace with `dict`

app/schemas_pydantic/scene.py:94:25: UP007 [*] Use `X | Y` for type annotations
   |
92 |     lighting_config: Optional[Dict[str, Any]] = None
93 |     camera_config: Optional[Dict[str, Any]] = None
94 |     interaction_config: Optional[Dict[str, Any]] = None
   |                         ^^^^^^^^^^^^^^^^^^^^^^^^ UP007
95 |     audio_config: Optional[Dict[str, Any]] = None
96 |     is_active: Optional[bool] = None
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/scene.py:94:34: UP006 [*] Use `dict` instead of `Dict` for type annotation
   |
92 |     lighting_config: Optional[Dict[str, Any]] = None
93 |     camera_config: Optional[Dict[str, Any]] = None
94 |     interaction_config: Optional[Dict[str, Any]] = None
   |                                  ^^^^ UP006
95 |     audio_config: Optional[Dict[str, Any]] = None
96 |     is_active: Optional[bool] = None
   |
   = help: Replace with `dict`

app/schemas_pydantic/scene.py:95:19: UP007 [*] Use `X | Y` for type annotations
   |
93 |     camera_config: Optional[Dict[str, Any]] = None
94 |     interaction_config: Optional[Dict[str, Any]] = None
95 |     audio_config: Optional[Dict[str, Any]] = None
   |                   ^^^^^^^^^^^^^^^^^^^^^^^^ UP007
96 |     is_active: Optional[bool] = None
97 |     is_premium: Optional[bool] = None
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/scene.py:95:28: UP006 [*] Use `dict` instead of `Dict` for type annotation
   |
93 |     camera_config: Optional[Dict[str, Any]] = None
94 |     interaction_config: Optional[Dict[str, Any]] = None
95 |     audio_config: Optional[Dict[str, Any]] = None
   |                            ^^^^ UP006
96 |     is_active: Optional[bool] = None
97 |     is_premium: Optional[bool] = None
   |
   = help: Replace with `dict`

app/schemas_pydantic/scene.py:96:16: UP007 [*] Use `X | Y` for type annotations
   |
94 |     interaction_config: Optional[Dict[str, Any]] = None
95 |     audio_config: Optional[Dict[str, Any]] = None
96 |     is_active: Optional[bool] = None
   |                ^^^^^^^^^^^^^^ UP007
97 |     is_premium: Optional[bool] = None
98 |     supports_mobile: Optional[bool] = None
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/scene.py:97:17: UP007 [*] Use `X | Y` for type annotations
   |
95 |     audio_config: Optional[Dict[str, Any]] = None
96 |     is_active: Optional[bool] = None
97 |     is_premium: Optional[bool] = None
   |                 ^^^^^^^^^^^^^^ UP007
98 |     supports_mobile: Optional[bool] = None
99 |     min_performance_level: Optional[str] = None
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/scene.py:98:22: UP007 [*] Use `X | Y` for type annotations
   |
96 |     is_active: Optional[bool] = None
97 |     is_premium: Optional[bool] = None
98 |     supports_mobile: Optional[bool] = None
   |                      ^^^^^^^^^^^^^^ UP007
99 |     min_performance_level: Optional[str] = None
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/scene.py:99:28: UP007 [*] Use `X | Y` for type annotations
   |
97 |     is_premium: Optional[bool] = None
98 |     supports_mobile: Optional[bool] = None
99 |     min_performance_level: Optional[str] = None
   |                            ^^^^^^^^^^^^^ UP007
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/scene.py:123:18: UP007 [*] Use `X | Y` for type annotations
    |
121 |     id: UUID
122 |     name: str
123 |     description: Optional[str]
    |                  ^^^^^^^^^^^^^ UP007
124 |     category: str
125 |     thumbnail_url: Optional[str]
    |
    = help: Convert to `X | Y`

app/schemas_pydantic/scene.py:125:20: UP007 [*] Use `X | Y` for type annotations
    |
123 |     description: Optional[str]
124 |     category: str
125 |     thumbnail_url: Optional[str]
    |                    ^^^^^^^^^^^^^ UP007
126 |     is_premium: bool
127 |     supports_mobile: bool
    |
    = help: Convert to `X | Y`

app/schemas_pydantic/scene.py:142:15: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
140 |         ..., description="交互类型：offering, candle, photo_display, message_board"
141 |     )
142 |     position: Dict[str, float] = Field(..., description="3D位置坐标")
    |               ^^^^ UP006
143 |     rotation: Optional[Dict[str, float]] = Field(default=None, description="旋转角度")
144 |     scale: Optional[Dict[str, float]] = Field(default=None, description="缩放比例")
    |
    = help: Replace with `dict`

app/schemas_pydantic/scene.py:143:15: UP007 [*] Use `X | Y` for type annotations
    |
141 |     )
142 |     position: Dict[str, float] = Field(..., description="3D位置坐标")
143 |     rotation: Optional[Dict[str, float]] = Field(default=None, description="旋转角度")
    |               ^^^^^^^^^^^^^^^^^^^^^^^^^^ UP007
144 |     scale: Optional[Dict[str, float]] = Field(default=None, description="缩放比例")
145 |     config: Optional[Dict[str, Any]] = Field(default=None, description="交互配置")
    |
    = help: Convert to `X | Y`

app/schemas_pydantic/scene.py:143:24: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
141 |     )
142 |     position: Dict[str, float] = Field(..., description="3D位置坐标")
143 |     rotation: Optional[Dict[str, float]] = Field(default=None, description="旋转角度")
    |                        ^^^^ UP006
144 |     scale: Optional[Dict[str, float]] = Field(default=None, description="缩放比例")
145 |     config: Optional[Dict[str, Any]] = Field(default=None, description="交互配置")
    |
    = help: Replace with `dict`

app/schemas_pydantic/scene.py:144:12: UP007 [*] Use `X | Y` for type annotations
    |
142 |     position: Dict[str, float] = Field(..., description="3D位置坐标")
143 |     rotation: Optional[Dict[str, float]] = Field(default=None, description="旋转角度")
144 |     scale: Optional[Dict[str, float]] = Field(default=None, description="缩放比例")
    |            ^^^^^^^^^^^^^^^^^^^^^^^^^^ UP007
145 |     config: Optional[Dict[str, Any]] = Field(default=None, description="交互配置")
    |
    = help: Convert to `X | Y`

app/schemas_pydantic/scene.py:144:21: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
142 |     position: Dict[str, float] = Field(..., description="3D位置坐标")
143 |     rotation: Optional[Dict[str, float]] = Field(default=None, description="旋转角度")
144 |     scale: Optional[Dict[str, float]] = Field(default=None, description="缩放比例")
    |                     ^^^^ UP006
145 |     config: Optional[Dict[str, Any]] = Field(default=None, description="交互配置")
    |
    = help: Replace with `dict`

app/schemas_pydantic/scene.py:145:13: UP007 [*] Use `X | Y` for type annotations
    |
143 |     rotation: Optional[Dict[str, float]] = Field(default=None, description="旋转角度")
144 |     scale: Optional[Dict[str, float]] = Field(default=None, description="缩放比例")
145 |     config: Optional[Dict[str, Any]] = Field(default=None, description="交互配置")
    |             ^^^^^^^^^^^^^^^^^^^^^^^^ UP007
    |
    = help: Convert to `X | Y`

app/schemas_pydantic/scene.py:145:22: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
143 |     rotation: Optional[Dict[str, float]] = Field(default=None, description="旋转角度")
144 |     scale: Optional[Dict[str, float]] = Field(default=None, description="缩放比例")
145 |     config: Optional[Dict[str, Any]] = Field(default=None, description="交互配置")
    |                      ^^^^ UP006
    |
    = help: Replace with `dict`

app/schemas_pydantic/tribute.py:3:1: UP035 `typing.Dict` is deprecated, use `dict` instead
  |
1 | # 祭拜功能相关的 Pydantic 模式
2 | from datetime import datetime
3 | from typing import Dict, List, Optional
  | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP035
4 | from uuid import UUID
  |

app/schemas_pydantic/tribute.py:3:1: UP035 `typing.List` is deprecated, use `list` instead
  |
1 | # 祭拜功能相关的 Pydantic 模式
2 | from datetime import datetime
3 | from typing import Dict, List, Optional
  | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP035
4 | from uuid import UUID
  |

app/schemas_pydantic/tribute.py:15:15: UP007 [*] Use `X | Y` for type annotations
   |
13 |     name: str = Field(..., description="物品名称")
14 |     quantity: int = Field(default=1, description="数量")
15 |     position: Optional[Dict] = Field(None, description="3D位置坐标 {x, y, z}")
   |               ^^^^^^^^^^^^^^ UP007
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/tribute.py:15:24: UP006 [*] Use `dict` instead of `Dict` for type annotation
   |
13 |     name: str = Field(..., description="物品名称")
14 |     quantity: int = Field(default=1, description="数量")
15 |     position: Optional[Dict] = Field(None, description="3D位置坐标 {x, y, z}")
   |                        ^^^^ UP006
   |
   = help: Replace with `dict`

app/schemas_pydantic/tribute.py:21:18: UP007 [*] Use `X | Y` for type annotations
   |
19 |     """客户端信息"""
20 |
21 |     device_type: Optional[str] = Field(None, description="设备类型")
   |                  ^^^^^^^^^^^^^ UP007
22 |     browser: Optional[str] = Field(None, description="浏览器信息")
23 |     screen_resolution: Optional[str] = Field(None, description="屏幕分辨率")
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/tribute.py:22:14: UP007 [*] Use `X | Y` for type annotations
   |
21 |     device_type: Optional[str] = Field(None, description="设备类型")
22 |     browser: Optional[str] = Field(None, description="浏览器信息")
   |              ^^^^^^^^^^^^^ UP007
23 |     screen_resolution: Optional[str] = Field(None, description="屏幕分辨率")
24 |     user_agent: Optional[str] = Field(None, description="用户代理")
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/tribute.py:23:24: UP007 [*] Use `X | Y` for type annotations
   |
21 |     device_type: Optional[str] = Field(None, description="设备类型")
22 |     browser: Optional[str] = Field(None, description="浏览器信息")
23 |     screen_resolution: Optional[str] = Field(None, description="屏幕分辨率")
   |                        ^^^^^^^^^^^^^ UP007
24 |     user_agent: Optional[str] = Field(None, description="用户代理")
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/tribute.py:24:17: UP007 [*] Use `X | Y` for type annotations
   |
22 |     browser: Optional[str] = Field(None, description="浏览器信息")
23 |     screen_resolution: Optional[str] = Field(None, description="屏幕分辨率")
24 |     user_agent: Optional[str] = Field(None, description="用户代理")
   |                 ^^^^^^^^^^^^^ UP007
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/tribute.py:33:14: UP007 [*] Use `X | Y` for type annotations
   |
31 |         ..., description="祭拜类型：candle, flower, incense, food, bow, offering"
32 |     )
33 |     message: Optional[str] = Field(None, description="祭拜留言或寄语")
   |              ^^^^^^^^^^^^^ UP007
34 |     is_anonymous: bool = Field(default=False, description="是否匿名祭拜")
35 |     tribute_items: Optional[List[TributeItem]] = Field(None, description="祭拜物品列表")
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/tribute.py:35:20: UP007 [*] Use `X | Y` for type annotations
   |
33 |     message: Optional[str] = Field(None, description="祭拜留言或寄语")
34 |     is_anonymous: bool = Field(default=False, description="是否匿名祭拜")
35 |     tribute_items: Optional[List[TributeItem]] = Field(None, description="祭拜物品列表")
   |                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP007
36 |     duration_seconds: Optional[int] = Field(default=0, description="祭拜持续时间（秒）")
37 |     coordinates: Optional[Dict] = Field(None, description="3D场景中的坐标位置 {x, y, z}")
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/tribute.py:35:29: UP006 [*] Use `list` instead of `List` for type annotation
   |
33 |     message: Optional[str] = Field(None, description="祭拜留言或寄语")
34 |     is_anonymous: bool = Field(default=False, description="是否匿名祭拜")
35 |     tribute_items: Optional[List[TributeItem]] = Field(None, description="祭拜物品列表")
   |                             ^^^^ UP006
36 |     duration_seconds: Optional[int] = Field(default=0, description="祭拜持续时间（秒）")
37 |     coordinates: Optional[Dict] = Field(None, description="3D场景中的坐标位置 {x, y, z}")
   |
   = help: Replace with `list`

app/schemas_pydantic/tribute.py:36:23: UP007 [*] Use `X | Y` for type annotations
   |
34 |     is_anonymous: bool = Field(default=False, description="是否匿名祭拜")
35 |     tribute_items: Optional[List[TributeItem]] = Field(None, description="祭拜物品列表")
36 |     duration_seconds: Optional[int] = Field(default=0, description="祭拜持续时间（秒）")
   |                       ^^^^^^^^^^^^^ UP007
37 |     coordinates: Optional[Dict] = Field(None, description="3D场景中的坐标位置 {x, y, z}")
38 |     client_info: Optional[ClientInfo] = Field(None, description="客户端信息")
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/tribute.py:37:18: UP007 [*] Use `X | Y` for type annotations
   |
35 |     tribute_items: Optional[List[TributeItem]] = Field(None, description="祭拜物品列表")
36 |     duration_seconds: Optional[int] = Field(default=0, description="祭拜持续时间（秒）")
37 |     coordinates: Optional[Dict] = Field(None, description="3D场景中的坐标位置 {x, y, z}")
   |                  ^^^^^^^^^^^^^^ UP007
38 |     client_info: Optional[ClientInfo] = Field(None, description="客户端信息")
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/tribute.py:37:27: UP006 [*] Use `dict` instead of `Dict` for type annotation
   |
35 |     tribute_items: Optional[List[TributeItem]] = Field(None, description="祭拜物品列表")
36 |     duration_seconds: Optional[int] = Field(default=0, description="祭拜持续时间（秒）")
37 |     coordinates: Optional[Dict] = Field(None, description="3D场景中的坐标位置 {x, y, z}")
   |                           ^^^^ UP006
38 |     client_info: Optional[ClientInfo] = Field(None, description="客户端信息")
   |
   = help: Replace with `dict`

app/schemas_pydantic/tribute.py:38:18: UP007 [*] Use `X | Y` for type annotations
   |
36 |     duration_seconds: Optional[int] = Field(default=0, description="祭拜持续时间（秒）")
37 |     coordinates: Optional[Dict] = Field(None, description="3D场景中的坐标位置 {x, y, z}")
38 |     client_info: Optional[ClientInfo] = Field(None, description="客户端信息")
   |                  ^^^^^^^^^^^^^^^^^^^^ UP007
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/tribute.py:53:16: UP007 [*] Use `X | Y` for type annotations
   |
51 |     memorial_space_id: UUID
52 |     user_id: UUID
53 |     user_name: Optional[str] = None
   |                ^^^^^^^^^^^^^ UP007
54 |     created_at: datetime
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/tribute.py:63:12: UP006 [*] Use `list` instead of `List` for type annotation
   |
61 |     """祭拜记录列表响应模式"""
62 |
63 |     items: List[TributeResponse]
   |            ^^^^ UP006
64 |     total: int
65 |     skip: int = 0
   |
   = help: Replace with `list`

app/schemas_pydantic/tribute.py:74:20: UP006 [*] Use `dict` instead of `Dict` for type annotation
   |
72 |     memorial_space_id: UUID
73 |     total_tributes: int
74 |     tribute_types: Dict[str, int] = Field(description="各类型祭拜次数统计")
   |                    ^^^^ UP006
75 |     recent_tributes: List[TributeResponse] = Field(description="最近的祭拜记录")
   |
   = help: Replace with `dict`

app/schemas_pydantic/tribute.py:75:22: UP006 [*] Use `list` instead of `List` for type annotation
   |
73 |     total_tributes: int
74 |     tribute_types: Dict[str, int] = Field(description="各类型祭拜次数统计")
75 |     recent_tributes: List[TributeResponse] = Field(description="最近的祭拜记录")
   |                      ^^^^ UP006
   |
   = help: Replace with `list`

app/schemas_pydantic/tribute.py:82:18: UP007 [*] Use `X | Y` for type annotations
   |
81 |     name: str = Field(..., min_length=1, max_length=100)
82 |     description: Optional[str] = None
   |                  ^^^^^^^^^^^^^ UP007
83 |     category: str = Field(
84 |         ..., description="祭品类别：flower, candle, incense, food, drink, fruit"
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/tribute.py:86:16: UP007 [*] Use `X | Y` for type annotations
   |
84 |         ..., description="祭品类别：flower, candle, incense, food, drink, fruit"
85 |     )
86 |     image_url: Optional[str] = None
   |                ^^^^^^^^^^^^^ UP007
87 |     model_url: Optional[str] = Field(None, description="3D模型URL")
88 |     price: float = Field(default=0, ge=0, description="价格")
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/tribute.py:87:16: UP007 [*] Use `X | Y` for type annotations
   |
85 |     )
86 |     image_url: Optional[str] = None
87 |     model_url: Optional[str] = Field(None, description="3D模型URL")
   |                ^^^^^^^^^^^^^ UP007
88 |     price: float = Field(default=0, ge=0, description="价格")
   |
   = help: Convert to `X | Y`

app/schemas_pydantic/tribute.py:113:12: UP006 [*] Use `list` instead of `List` for type annotation
    |
111 |     """祭品列表响应模式"""
112 |
113 |     items: List[TributeItemResponse]
    |            ^^^^ UP006
114 |     total: int
115 |     categories: List[str] = Field(description="可用的祭品类别")
    |
    = help: Replace with `list`

app/schemas_pydantic/tribute.py:115:17: UP006 [*] Use `list` instead of `List` for type annotation
    |
113 |     items: List[TributeItemResponse]
114 |     total: int
115 |     categories: List[str] = Field(description="可用的祭品类别")
    |                 ^^^^ UP006
    |
    = help: Replace with `list`

app/schemas_pydantic/tribute.py:124:23: UP007 [*] Use `X | Y` for type annotations
    |
122 |         default="individual", description="会话类型：individual, group, family"
123 |     )
124 |     duration_minutes: Optional[int] = Field(None, description="预计祭拜时长（分钟）")
    |                       ^^^^^^^^^^^^^ UP007
125 |     participant_count: int = Field(default=1, description="参与人数")
    |
    = help: Convert to `X | Y`

app/schemas_pydantic/tribute.py:142:15: UP007 [*] Use `X | Y` for type annotations
    |
140 |     status: str = Field(description="会话状态：active, completed, cancelled")
141 |     started_at: datetime
142 |     ended_at: Optional[datetime] = None
    |               ^^^^^^^^^^^^^^^^^^ UP007
143 |     tribute_count: int = Field(default=0, description="本次会话的祭拜次数")
    |
    = help: Convert to `X | Y`

app/services/analytics_service.py:2:1: UP035 `typing.Dict` is deprecated, use `dict` instead
  |
1 | # 商业化数据分析服务
2 | from typing import Dict, List, Optional, Any
  | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP035
3 | from datetime import datetime, timedelta
4 | import json
  |

app/services/analytics_service.py:2:1: UP035 `typing.List` is deprecated, use `list` instead
  |
1 | # 商业化数据分析服务
2 | from typing import Dict, List, Optional, Any
  | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP035
3 | from datetime import datetime, timedelta
4 | import json
  |

app/services/analytics_service.py:2:1: I001 [*] Import block is un-sorted or un-formatted
  |
1 |   # 商业化数据分析服务
2 | / from typing import Dict, List, Optional, Any
3 | | from datetime import datetime, timedelta
4 | | import json
5 | | from sqlalchemy.orm import Session
6 | | from sqlalchemy import func, text
7 | | from app.models_sqlalchemy import User
8 | | from app.services.membership_service import MembershipService, MembershipTier
  | |_____________________________________________________________________________^ I001
  |
  = help: Organize imports

app/services/analytics_service.py:2:32: F401 [*] `typing.Optional` imported but unused
  |
1 | # 商业化数据分析服务
2 | from typing import Dict, List, Optional, Any
  |                                ^^^^^^^^ F401
3 | from datetime import datetime, timedelta
4 | import json
  |
  = help: Remove unused import: `typing.Optional`

app/services/analytics_service.py:4:8: F401 [*] `json` imported but unused
  |
2 | from typing import Dict, List, Optional, Any
3 | from datetime import datetime, timedelta
4 | import json
  |        ^^^^ F401
5 | from sqlalchemy.orm import Session
6 | from sqlalchemy import func, text
  |
  = help: Remove unused import: `json`

app/services/analytics_service.py:6:24: F401 [*] `sqlalchemy.func` imported but unused
  |
4 | import json
5 | from sqlalchemy.orm import Session
6 | from sqlalchemy import func, text
  |                        ^^^^ F401
7 | from app.models_sqlalchemy import User
8 | from app.services.membership_service import MembershipService, MembershipTier
  |
  = help: Remove unused import

app/services/analytics_service.py:6:30: F401 [*] `sqlalchemy.text` imported but unused
  |
4 | import json
5 | from sqlalchemy.orm import Session
6 | from sqlalchemy import func, text
  |                              ^^^^ F401
7 | from app.models_sqlalchemy import User
8 | from app.services.membership_service import MembershipService, MembershipTier
  |
  = help: Remove unused import

app/services/analytics_service.py:18:66: UP006 [*] Use `dict` instead of `Dict` for type annotation
   |
16 |         self.membership_service = MembershipService(db)
17 |
18 |     def get_business_analytics(self, date_range: str = "30d") -> Dict[str, Any]:
   |                                                                  ^^^^ UP006
19 |         """获取商业化分析数据"""
20 |         end_date = datetime.now()
   |
   = help: Replace with `dict`

app/services/analytics_service.py:46:10: UP006 [*] Use `dict` instead of `Dict` for type annotation
   |
44 |     def _get_revenue_metrics(
45 |         self, start_date: datetime, end_date: datetime
46 |     ) -> Dict[str, Any]:
   |          ^^^^ UP006
47 |         """获取收入指标"""
48 |         # 模拟数据 - 在实际实现中应该从数据库查询
   |
   = help: Replace with `dict`

app/services/analytics_service.py:92:10: UP006 [*] Use `dict` instead of `Dict` for type annotation
   |
90 |     def _get_user_metrics(
91 |         self, start_date: datetime, end_date: datetime
92 |     ) -> Dict[str, Any]:
   |          ^^^^ UP006
93 |         """获取用户指标"""
94 |         # 在实际实现中，这些应该从数据库查询
   |
   = help: Replace with `dict`

app/services/analytics_service.py:111:10: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
109 |     def _get_conversion_metrics(
110 |         self, start_date: datetime, end_date: datetime
111 |     ) -> Dict[str, Any]:
    |          ^^^^ UP006
112 |         """获取转化指标"""
113 |         return {
    |
    = help: Replace with `dict`

app/services/analytics_service.py:167:10: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
165 |     def _get_retention_metrics(
166 |         self, start_date: datetime, end_date: datetime
167 |     ) -> Dict[str, Any]:
    |          ^^^^ UP006
168 |         """获取留存指标"""
169 |         return {
    |
    = help: Replace with `dict`

app/services/analytics_service.py:189:10: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
187 |     def _get_product_metrics(
188 |         self, start_date: datetime, end_date: datetime
189 |     ) -> Dict[str, Any]:
    |          ^^^^ UP006
190 |         """获取产品指标"""
191 |         return {
    |
    = help: Replace with `dict`

app/services/analytics_service.py:287:10: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
285 |     def _get_growth_metrics(
286 |         self, start_date: datetime, end_date: datetime
287 |     ) -> Dict[str, Any]:
    |          ^^^^ UP006
288 |         """获取增长指标"""
289 |         return {
    |
    = help: Replace with `dict`

app/services/analytics_service.py:300:10: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
298 |     def _get_membership_analytics(
299 |         self, start_date: datetime, end_date: datetime
300 |     ) -> Dict[str, Any]:
    |          ^^^^ UP006
301 |         """获取会员权益分析数据"""
302 |         # 统计各等级用户数量
    |
    = help: Replace with `dict`

app/services/analytics_service.py:478:28: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
476 |     def _calculate_membership_health_score(
477 |         self,
478 |         tier_distribution: Dict[str, int],
    |                            ^^^^ UP006
479 |         feature_usage: Dict[str, Any],
480 |         churn_analysis: Dict[str, Any],
    |
    = help: Replace with `dict`

app/services/analytics_service.py:479:24: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
477 |         self,
478 |         tier_distribution: Dict[str, int],
479 |         feature_usage: Dict[str, Any],
    |                        ^^^^ UP006
480 |         churn_analysis: Dict[str, Any],
481 |     ) -> float:
    |
    = help: Replace with `dict`

app/services/analytics_service.py:480:25: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
478 |         tier_distribution: Dict[str, int],
479 |         feature_usage: Dict[str, Any],
480 |         churn_analysis: Dict[str, Any],
    |                         ^^^^ UP006
481 |     ) -> float:
482 |         """计算会员健康度评分"""
    |
    = help: Replace with `dict`

app/services/analytics_service.py:513:10: UP006 [*] Use `list` instead of `List` for type annotation
    |
511 |     def _generate_revenue_trends(
512 |         self, start_date: datetime, end_date: datetime
513 |     ) -> List[Dict[str, Any]]:
    |          ^^^^ UP006
514 |         """生成收入趋势数据"""
515 |         trends = []
    |
    = help: Replace with `list`

app/services/analytics_service.py:513:15: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
511 |     def _generate_revenue_trends(
512 |         self, start_date: datetime, end_date: datetime
513 |     ) -> List[Dict[str, Any]]:
    |               ^^^^ UP006
514 |         """生成收入趋势数据"""
515 |         trends = []
    |
    = help: Replace with `dict`

app/services/analytics_service.py:544:49: UP006 [*] Use `list` instead of `List` for type annotation
    |
542 |         return trends
543 |
544 |     def get_membership_recommendations(self) -> List[Dict[str, Any]]:
    |                                                 ^^^^ UP006
545 |         """获取会员系统优化建议"""
546 |         recommendations = []
    |
    = help: Replace with `list`

app/services/analytics_service.py:544:54: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
542 |         return trends
543 |
544 |     def get_membership_recommendations(self) -> List[Dict[str, Any]]:
    |                                                      ^^^^ UP006
545 |         """获取会员系统优化建议"""
546 |         recommendations = []
    |
    = help: Replace with `dict`

app/services/analytics_service.py:561:36: UP032 [*] Use f-string instead of `format` call
    |
559 |                       "type": "urgent",
560 |                       "title": "会员健康度偏低",
561 |                       "description": "当前会员健康度评分为{:.1f}分，建议重点关注用户转化和留存".format(
    |  ____________________________________^
562 | |                         health_score
563 | |                     ),
    | |_____________________^ UP032
564 |                       "actions": ["优化免费版功能体验", "调整定价策略", "加强用户引导和教育"],
565 |                   }
    |
    = help: Convert to f-string

app/services/content_moderation.py:1:1: UP009 [*] UTF-8 encoding declaration is unnecessary
  |
1 | # -*- coding: utf-8 -*-
  | ^^^^^^^^^^^^^^^^^^^^^^^ UP009
2 | """
3 | 内容审核服务模块
  |
  = help: Remove unnecessary coding comment

app/services/content_moderation.py:6:1: I001 [*] Import block is un-sorted or un-formatted
   |
 4 |   提供基础的内容过滤、敏感词检测和内容质量评估功能
 5 |   """
 6 | / import re
 7 | | import hashlib
 8 | | from typing import List, Tuple, Optional
 9 | | from datetime import datetime, timedelta
10 | | from enum import Enum
11 | |
12 | | from sqlalchemy.orm import Session
13 | | from sqlalchemy import func
14 | |
15 | | from app import models_sqlalchemy as models
   | |___________________________________________^ I001
   |
   = help: Organize imports

app/services/content_moderation.py:8:1: UP035 `typing.List` is deprecated, use `list` instead
   |
 6 | import re
 7 | import hashlib
 8 | from typing import List, Tuple, Optional
   | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP035
 9 | from datetime import datetime, timedelta
10 | from enum import Enum
   |

app/services/content_moderation.py:8:1: UP035 `typing.Tuple` is deprecated, use `tuple` instead
   |
 6 | import re
 7 | import hashlib
 8 | from typing import List, Tuple, Optional
   | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP035
 9 | from datetime import datetime, timedelta
10 | from enum import Enum
   |

app/services/content_moderation.py:33:18: UP007 [*] Use `X | Y` for type annotations
   |
31 |         action: ModerationAction,
32 |         confidence: float,
33 |         reasons: Optional[List[str]] = None,
   |                  ^^^^^^^^^^^^^^^^^^^ UP007
34 |         filtered_content: Optional[str] = None,
35 |     ):
   |
   = help: Convert to `X | Y`

app/services/content_moderation.py:33:27: UP006 [*] Use `list` instead of `List` for type annotation
   |
31 |         action: ModerationAction,
32 |         confidence: float,
33 |         reasons: Optional[List[str]] = None,
   |                           ^^^^ UP006
34 |         filtered_content: Optional[str] = None,
35 |     ):
   |
   = help: Replace with `list`

app/services/content_moderation.py:34:27: UP007 [*] Use `X | Y` for type annotations
   |
32 |         confidence: float,
33 |         reasons: Optional[List[str]] = None,
34 |         filtered_content: Optional[str] = None,
   |                           ^^^^^^^^^^^^^ UP007
35 |     ):
36 |         self.action = action
   |
   = help: Convert to `X | Y`

app/services/content_moderation.py:188:9: C901 `moderate_content` is too complex (14 > 10)
    |
186 |         self.min_content_length = 2
187 |
188 |     def moderate_content(
    |         ^^^^^^^^^^^^^^^^ C901
189 |         self, content: str, user_id: Optional[str] = None
190 |     ) -> ModerationResult:
    |

app/services/content_moderation.py:189:38: UP007 [*] Use `X | Y` for type annotations
    |
188 |     def moderate_content(
189 |         self, content: str, user_id: Optional[str] = None
    |                                      ^^^^^^^^^^^^^ UP007
190 |     ) -> ModerationResult:
191 |         """
    |
    = help: Convert to `X | Y`

app/services/content_moderation.py:272:49: UP006 [*] Use `tuple` instead of `Tuple` for type annotation
    |
270 |         )
271 |
272 |     def _check_profanity(self, content: str) -> Tuple[float, List[str]]:
    |                                                 ^^^^^ UP006
273 |         """检查敏感词汇"""
274 |         content_lower = content.lower()
    |
    = help: Replace with `tuple`

app/services/content_moderation.py:272:62: UP006 [*] Use `list` instead of `List` for type annotation
    |
270 |         )
271 |
272 |     def _check_profanity(self, content: str) -> Tuple[float, List[str]]:
    |                                                              ^^^^ UP006
273 |         """检查敏感词汇"""
274 |         content_lower = content.lower()
    |
    = help: Replace with `list`

app/services/content_moderation.py:292:44: UP006 [*] Use `tuple` instead of `Tuple` for type annotation
    |
290 |         return score, reasons
291 |
292 |     def _check_spam(self, content: str) -> Tuple[float, List[str]]:
    |                                            ^^^^^ UP006
293 |         """检查垃圾信息模式"""
294 |         reasons: List[str] = []
    |
    = help: Replace with `tuple`

app/services/content_moderation.py:292:57: UP006 [*] Use `list` instead of `List` for type annotation
    |
290 |         return score, reasons
291 |
292 |     def _check_spam(self, content: str) -> Tuple[float, List[str]]:
    |                                                         ^^^^ UP006
293 |         """检查垃圾信息模式"""
294 |         reasons: List[str] = []
    |
    = help: Replace with `list`

app/services/content_moderation.py:294:18: UP006 [*] Use `list` instead of `List` for type annotation
    |
292 |     def _check_spam(self, content: str) -> Tuple[float, List[str]]:
293 |         """检查垃圾信息模式"""
294 |         reasons: List[str] = []
    |                  ^^^^ UP006
295 |         score = 0.0
    |
    = help: Replace with `list`

app/services/content_moderation.py:306:50: UP006 [*] Use `tuple` instead of `Tuple` for type annotation
    |
304 |         return min(score, 1.0), reasons
305 |
306 |     def _check_suspicious(self, content: str) -> Tuple[float, List[str]]:
    |                                                  ^^^^^ UP006
307 |         """检查可疑内容"""
308 |         content_lower = content.lower()
    |
    = help: Replace with `tuple`

app/services/content_moderation.py:306:63: UP006 [*] Use `list` instead of `List` for type annotation
    |
304 |         return min(score, 1.0), reasons
305 |
306 |     def _check_suspicious(self, content: str) -> Tuple[float, List[str]]:
    |                                                               ^^^^ UP006
307 |         """检查可疑内容"""
308 |         content_lower = content.lower()
    |
    = help: Replace with `list`

app/services/content_moderation.py:355:9: F841 Local variable `content_hash` is assigned to but never used
    |
354 |         # 检查内容相似度（简单的哈希比较）
355 |         content_hash = hashlib.md5(content.encode()).hexdigest()
    |         ^^^^^^^^^^^^ F841
356 |         similar_messages = (
357 |             db.query(func.count(models.MemorialMessage.id))
    |
    = help: Remove assignment to unused variable `content_hash`

app/services/content_moderation.py:401:17: E712 Avoid equality comparisons to `True`; use `models.MemorialMessage.is_active:` for truth checks
    |
399 |             .filter(
400 |                 models.MemorialMessage.created_at >= start_date,
401 |                 models.MemorialMessage.is_active == True,
    |                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ E712
402 |             )
403 |             .scalar()
    |
    = help: Replace with `models.MemorialMessage.is_active`

app/services/content_moderation.py:423:28: UP007 [*] Use `X | Y` for type annotations
    |
422 | def moderate_message_content(
423 |     content: str, user_id: Optional[str] = None
    |                            ^^^^^^^^^^^^^ UP007
424 | ) -> ModerationResult:
425 |     """
    |
    = help: Convert to `X | Y`

app/services/membership_service.py:2:1: UP035 `typing.Dict` is deprecated, use `dict` instead
  |
1 | # 会员权益管理服务
2 | from typing import Dict, List, Optional, Any
  | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP035
3 | from datetime import datetime, timedelta
4 | import json
  |

app/services/membership_service.py:2:1: UP035 `typing.List` is deprecated, use `list` instead
  |
1 | # 会员权益管理服务
2 | from typing import Dict, List, Optional, Any
  | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP035
3 | from datetime import datetime, timedelta
4 | import json
  |

app/services/membership_service.py:2:1: I001 [*] Import block is un-sorted or un-formatted
  |
1 |   # 会员权益管理服务
2 | / from typing import Dict, List, Optional, Any
3 | | from datetime import datetime, timedelta
4 | | import json
5 | | from sqlalchemy.orm import Session
6 | | from app.models_sqlalchemy import User
7 | | from app.schemas_pydantic.user import UserBase
  | |______________________________________________^ I001
  |
  = help: Organize imports

app/services/membership_service.py:2:32: F401 [*] `typing.Optional` imported but unused
  |
1 | # 会员权益管理服务
2 | from typing import Dict, List, Optional, Any
  |                                ^^^^^^^^ F401
3 | from datetime import datetime, timedelta
4 | import json
  |
  = help: Remove unused import: `typing.Optional`

app/services/membership_service.py:4:8: F401 [*] `json` imported but unused
  |
2 | from typing import Dict, List, Optional, Any
3 | from datetime import datetime, timedelta
4 | import json
  |        ^^^^ F401
5 | from sqlalchemy.orm import Session
6 | from app.models_sqlalchemy import User
  |
  = help: Remove unused import: `json`

app/services/membership_service.py:7:39: F401 [*] `app.schemas_pydantic.user.UserBase` imported but unused
  |
5 | from sqlalchemy.orm import Session
6 | from app.models_sqlalchemy import User
7 | from app.schemas_pydantic.user import UserBase
  |                                       ^^^^^^^^ F401
  |
  = help: Remove unused import: `app.schemas_pydantic.user.UserBase`

app/services/membership_service.py:95:47: UP006 [*] Use `dict` instead of `Dict` for type annotation
   |
93 |         self.db = db
94 |
95 |     def get_user_usage(self, user_id: str) -> Dict[str, Any]:
   |                                               ^^^^ UP006
96 |         """获取用户当前使用情况"""
97 |         # 这里应该查询实际的使用数据
   |
   = help: Replace with `dict`

app/services/membership_service.py:143:45: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
141 |         return getattr(user, "membership_tier", MembershipTier.FREE)
142 |
143 |     def get_tier_limits(self, tier: str) -> Dict[str, Any]:
    |                                             ^^^^ UP006
144 |         """获取等级限制"""
145 |         return self.config.TIER_CONFIGS.get(
    |
    = help: Replace with `dict`

app/services/membership_service.py:149:48: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
147 |         )
148 |
149 |     def get_user_limits(self, user_id: str) -> Dict[str, Any]:
    |                                                ^^^^ UP006
150 |         """获取用户的功能限制"""
151 |         tier = self.get_user_tier(user_id)
    |
    = help: Replace with `dict`

app/services/membership_service.py:156:10: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
154 |     def check_feature_access(
155 |         self, user_id: str, feature_type: str, requested_amount: int = 1
156 |     ) -> Dict[str, Any]:
    |          ^^^^ UP006
157 |         """检查功能访问权限"""
158 |         tier = self.get_user_tier(user_id)
    |
    = help: Replace with `dict`

app/services/membership_service.py:195:58: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
193 |             }
194 |
195 |     def can_create_memorial_space(self, user_id: str) -> Dict[str, Any]:
    |                                                          ^^^^ UP006
196 |         """检查是否可以创建纪念空间"""
197 |         return self.check_feature_access(user_id, FeatureType.MEMORIAL_SPACES)
    |
    = help: Replace with `dict`

app/services/membership_service.py:201:10: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
199 |     def can_use_ai_service(
200 |         self, user_id: str, minutes_needed: int = 1
201 |     ) -> Dict[str, Any]:
    |          ^^^^ UP006
202 |         """检查是否可以使用AI服务"""
203 |         return self.check_feature_access(
    |
    = help: Replace with `dict`

app/services/membership_service.py:207:69: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
205 |         )
206 |
207 |     def can_upload_file(self, user_id: str, file_size_gb: float) -> Dict[str, Any]:
    |                                                                     ^^^^ UP006
208 |         """检查是否可以上传文件"""
209 |         return self.check_feature_access(
    |
    = help: Replace with `dict`

app/services/membership_service.py:213:50: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
211 |         )
212 |
213 |     def can_make_api_call(self, user_id: str) -> Dict[str, Any]:
    |                                                  ^^^^ UP006
214 |         """检查是否可以进行API调用"""
215 |         return self.check_feature_access(user_id, FeatureType.API_CALLS)
    |
    = help: Replace with `dict`

app/services/membership_service.py:240:60: UP006 [*] Use `list` instead of `List` for type annotation
    |
238 |         return self.usage_tracker.increment_usage(user_id, feature_type, amount)
239 |
240 |     def get_upgrade_recommendations(self, user_id: str) -> List[Dict[str, Any]]:
    |                                                            ^^^^ UP006
241 |         """获取升级建议"""
242 |         current_tier = self.get_user_tier(user_id)
    |
    = help: Replace with `list`

app/services/membership_service.py:240:65: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
238 |         return self.usage_tracker.increment_usage(user_id, feature_type, amount)
239 |
240 |     def get_upgrade_recommendations(self, user_id: str) -> List[Dict[str, Any]]:
    |                                                                 ^^^^ UP006
241 |         """获取升级建议"""
242 |         current_tier = self.get_user_tier(user_id)
    |
    = help: Replace with `dict`

app/services/membership_service.py:256:32: UP038 Use `X | Y` in `isinstance` call instead of `(X, Y)`
    |
254 |             limit = current_limits.get(base_feature, 0)
255 |
256 |             if limit != -1 and isinstance(limit, (int, float)) and used >= limit * 0.8:
    |                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP038
257 |                 # 使用量超过80%，建议升级
258 |                 recommendations.append(
    |
    = help: Convert to `X | Y`

app/services/membership_service.py:289:55: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
287 |         return MembershipTier.PREMIUM
288 |
289 |     def get_membership_summary(self, user_id: str) -> Dict[str, Any]:
    |                                                       ^^^^ UP006
290 |         """获取会员权益概览"""
291 |         tier = self.get_user_tier(user_id)
    |
    = help: Replace with `dict`

app/services/membership_service.py:299:16: UP038 Use `X | Y` in `isinstance` call instead of `(X, Y)`
    |
297 |         usage_percentages = {}
298 |         for feature_type, limit in limits.items():
299 |             if isinstance(limit, (int, float)) and limit > 0:
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP038
300 |                 used = usage.get(f"{feature_type}_used", 0)
301 |                 usage_percentages[feature_type] = (used / limit) * 100
    |
    = help: Convert to `X | Y`

app/services/membership_service.py:340:10: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
338 |     def validate_operation(
339 |         self, user_id: str, operation_type: str, **kwargs
340 |     ) -> Dict[str, Any]:
    |          ^^^^ UP006
341 |         """验证操作权限"""
342 |         """
    |
    = help: Replace with `dict`

app/services/replicate_ai_service.py:2:1: I001 [*] Import block is un-sorted or un-formatted
   |
 1 |   # Replicate AI 服务
 2 | / import os
 3 | | import logging
 4 | | from typing import Dict, Any, Optional
 5 | | import requests
 6 | | import asyncio
 7 | | import aiohttp
 8 | | from pathlib import Path
   | |________________________^ I001
 9 |
10 |   logger = logging.getLogger(__name__)
   |
   = help: Organize imports

app/services/replicate_ai_service.py:4:1: UP035 `typing.Dict` is deprecated, use `dict` instead
  |
2 | import os
3 | import logging
4 | from typing import Dict, Any, Optional
  | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ UP035
5 | import requests
6 | import asyncio
  |

app/services/replicate_ai_service.py:4:31: F401 [*] `typing.Optional` imported but unused
  |
2 | import os
3 | import logging
4 | from typing import Dict, Any, Optional
  |                               ^^^^^^^^ F401
5 | import requests
6 | import asyncio
  |
  = help: Remove unused import: `typing.Optional`

app/services/replicate_ai_service.py:5:8: F401 [*] `requests` imported but unused
  |
3 | import logging
4 | from typing import Dict, Any, Optional
5 | import requests
  |        ^^^^^^^^ F401
6 | import asyncio
7 | import aiohttp
  |
  = help: Remove unused import: `requests`

app/services/replicate_ai_service.py:8:21: F401 [*] `pathlib.Path` imported but unused
   |
 6 | import asyncio
 7 | import aiohttp
 8 | from pathlib import Path
   |                     ^^^^ F401
 9 |
10 | logger = logging.getLogger(__name__)
   |
   = help: Remove unused import: `pathlib.Path`

app/services/replicate_ai_service.py:26:54: UP006 [*] Use `dict` instead of `Dict` for type annotation
   |
24 |         }
25 |
26 |     async def restore_photo(self, image_url: str) -> Dict[str, Any]:
   |                                                      ^^^^ UP006
27 |         """
28 |         使用AI修复老照片
   |
   = help: Replace with `dict`

app/services/replicate_ai_service.py:56:21: B007 Loop control variable `attempt` not used within loop body
   |
54 |                 # 轮询结果
55 |                 max_attempts = 60  # 最多等待5分钟
56 |                 for attempt in range(max_attempts):
   |                     ^^^^^^^ B007
57 |                     async with session.get(
58 |                         f"{self.base_url}/predictions/{prediction_id}",
   |
   = help: Rename unused `attempt` to `_attempt`

app/services/replicate_ai_service.py:95:10: UP006 [*] Use `dict` instead of `Dict` for type annotation
   |
93 |     async def enhance_photo(
94 |         self, image_url: str, scale: int = 4, face_enhance: bool = True
95 |     ) -> Dict[str, Any]:
   |          ^^^^ UP006
96 |         """
97 |         使用AI增强照片分辨率
   |
   = help: Replace with `dict`

app/services/replicate_ai_service.py:129:21: B007 Loop control variable `attempt` not used within loop body
    |
127 |                 # 轮询结果
128 |                 max_attempts = 60
129 |                 for attempt in range(max_attempts):
    |                     ^^^^^^^ B007
130 |                     async with session.get(
131 |                         f"{self.base_url}/predictions/{prediction_id}",
    |
    = help: Rename unused `attempt` to `_attempt`

app/services/replicate_ai_service.py:166:10: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
164 |     async def remove_background(
165 |         self, image_url: str, model: str = "u2net"
166 |     ) -> Dict[str, Any]:
    |          ^^^^ UP006
167 |         """
168 |         使用AI移除照片背景
    |
    = help: Replace with `dict`

app/services/replicate_ai_service.py:196:21: B007 Loop control variable `attempt` not used within loop body
    |
194 |                 # 轮询结果
195 |                 max_attempts = 30  # 背景移除通常较快
196 |                 for attempt in range(max_attempts):
    |                     ^^^^^^^ B007
197 |                     async with session.get(
198 |                         f"{self.base_url}/predictions/{prediction_id}",
    |
    = help: Rename unused `attempt` to `_attempt`

app/services/replicate_ai_service.py:230:55: UP006 [*] Use `dict` instead of `Dict` for type annotation
    |
228 |             return {"success": False, "error": f"背景移除失败: {str(e)}"}
229 |
230 |     async def colorize_photo(self, image_url: str) -> Dict[str, Any]:
    |                                                       ^^^^ UP006
231 |         """
232 |         使用AI为黑白照片上色
    |
    = help: Replace with `dict`

app/services/replicate_ai_service.py:260:21: B007 Loop control variable `attempt` not used within loop body
    |
258 |                 # 轮询结果
259 |                 max_attempts = 60
260 |                 for attempt in range(max_attempts):
    |                     ^^^^^^^ B007
261 |                     async with session.get(
262 |                         f"{self.base_url}/predictions/{prediction_id}",
    |
    = help: Rename unused `attempt` to `_attempt`

Found 490 errors.
[*] 364 fixable with the `--fix` option (27 hidden fixes can be enabled with the `--unsafe-fixes` option).
