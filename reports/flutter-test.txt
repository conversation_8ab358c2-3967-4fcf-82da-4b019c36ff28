
00:00 +0: loading /Volumes/acasis/memorial/flutter/test/widget_test.dart                                                                                                                               test/ai_services_test.dart:18:8: Error: Error when reading 'test/ai_services_test.mocks.dart': No such file or directory
import 'ai_services_test.mocks.dart';
       ^

00:01 +0: loading /Volumes/acasis/memorial/flutter/test/widget_test.dart                                                                                                                               
00:01 +0: /Volumes/acasis/memorial/flutter/test/widget_test.dart: Memorial app smoke test                                                                                                              test/ai_services_test.dart:22:10: Error: 'MockClient' isn't a type.
    late MockClient mockClient;
         ^^^^^^^^^^
test/ai_services_test.dart:23:10: Error: 'MockAIService' isn't a type.
    late MockAIService mockAIService;
         ^^^^^^^^^^^^^
test/ai_services_test.dart:27:20: Error: Method not found: 'MockClient'.
      mockClient = MockClient();
                   ^^^^^^^^^^
test/ai_services_test.dart:28:23: Error: Method not found: 'MockAIService'.
      mockAIService = MockAIService();
                      ^^^^^^^^^^^^^

00:01 +0: /Volumes/acasis/memorial/flutter/test/widget_test.dart: Memorial app smoke test                                                                                                              
══╡ EXCEPTION CAUGHT BY IMAGE RESOURCE SERVICE ╞════════════════════════════════════════════════════
The following _Exception was thrown resolving an image codec:
Exception: Invalid image data

When the exception was thrown, this was the stack:
#0      _futurize (dart:ui/painting.dart:7977:5)
#1      ImageDescriptor.encoded (dart:ui/painting.dart:7771:12)
#2      instantiateImageCodecWithSize (dart:ui/painting.dart:2558:60)
#3      PaintingBinding.instantiateImageCodecWithSize (package:flutter/src/painting/binding.dart:147:15)
#4      AssetBundleImageProvider._loadAsync (package:flutter/src/painting/image_provider.dart:792:18)
<asynchronous suspension>
#5      MultiFrameImageStreamCompleter._handleCodecReady (package:flutter/src/painting/image_stream.dart:1027:3)
<asynchronous suspension>

Image provider: AssetImage(bundle: null, name: "assets/images/hero_background.jpg")
Image key: AssetBundleImageKey(bundle: PlatformAssetBundle#66088(), name:
  "assets/images/hero_background.jpg", scale: 1.0)
════════════════════════════════════════════════════════════════════════════════════════════════════

00:01 +0 -1: /Volumes/acasis/memorial/flutter/test/widget_test.dart: Memorial app smoke test [E]                                                                                                       
  Test failed. See exception logs above.
  The test description was: Memorial app smoke test
  

To run this test again: /opt/homebrew/Caskroom/flutter/3.32.1/flutter/bin/cache/dart-sdk/bin/dart test /Volumes/acasis/memorial/flutter/test/widget_test.dart -p vm --plain-name 'Memorial app smoke test'

00:02 +0 -2: loading /Volumes/acasis/memorial/flutter/test/ai_services_test.dart [E]                                                                                                                   
  Failed to load "/Volumes/acasis/memorial/flutter/test/ai_services_test.dart":
  Compilation failed for testPath=/Volumes/acasis/memorial/flutter/test/ai_services_test.dart: test/ai_services_test.dart:18:8: Error: Error when reading 'test/ai_services_test.mocks.dart': No such file or directory
  import 'ai_services_test.mocks.dart';
         ^
  test/ai_services_test.dart:22:10: Error: 'MockClient' isn't a type.
      late MockClient mockClient;
           ^^^^^^^^^^
  test/ai_services_test.dart:23:10: Error: 'MockAIService' isn't a type.
      late MockAIService mockAIService;
           ^^^^^^^^^^^^^
  test/ai_services_test.dart:27:20: Error: Method not found: 'MockClient'.
        mockClient = MockClient();
                     ^^^^^^^^^^
  test/ai_services_test.dart:28:23: Error: Method not found: 'MockAIService'.
        mockAIService = MockAIService();
                        ^^^^^^^^^^^^^
  .

To run this test again: /opt/homebrew/Caskroom/flutter/3.32.1/flutter/bin/cache/dart-sdk/bin/dart test /Volumes/acasis/memorial/flutter/test/ai_services_test.dart -p vm --plain-name 'loading /Volumes/acasis/memorial/flutter/test/ai_services_test.dart'

00:02 +0 -2: Some tests failed.                                                                                                                                                                        
