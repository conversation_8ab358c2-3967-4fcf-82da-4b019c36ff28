Analyzing flutter...                                            

   info • Use 'package:' imports for files in the 'lib' directory • lib/core/providers/ai_provider.dart:4:8 • always_use_package_imports
   info • Constructor declarations should be before non-constructor declarations • lib/core/providers/ai_provider.dart:23:3 • sort_constructors_first
   info • Catch clause should use 'on' to specify the type of exception being caught • lib/core/providers/ai_provider.dart:45:7 • avoid_catches_without_on_clauses
   info • Catch clause should use 'on' to specify the type of exception being caught • lib/core/providers/ai_provider.dart:58:7 • avoid_catches_without_on_clauses
   info • Catch clause should use 'on' to specify the type of exception being caught • lib/core/providers/ai_provider.dart:71:7 • avoid_catches_without_on_clauses
   info • Catch clause should use 'on' to specify the type of exception being caught • lib/core/providers/ai_provider.dart:103:7 • avoid_catches_without_on_clauses
   info • Catch clause should use 'on' to specify the type of exception being caught • lib/core/providers/ai_provider.dart:140:7 • avoid_catches_without_on_clauses
   info • Catch clause should use 'on' to specify the type of exception being caught • lib/core/providers/ai_provider.dart:161:7 • avoid_catches_without_on_clauses
   info • Catch clause should use 'on' to specify the type of exception being caught • lib/core/providers/ai_provider.dart:176:7 • avoid_catches_without_on_clauses
   info • Catch clause should use 'on' to specify the type of exception being caught • lib/core/providers/ai_provider.dart:207:7 • avoid_catches_without_on_clauses
   info • Catch clause should use 'on' to specify the type of exception being caught • lib/core/providers/ai_provider.dart:230:7 • avoid_catches_without_on_clauses
   info • Unnecessary use of a block function body • lib/core/providers/ai_provider.dart:263:40 • prefer_expression_function_bodies
   info • Unnecessary use of a block function body • lib/core/providers/ai_provider.dart:268:38 • prefer_expression_function_bodies
   info • Unnecessary use of a block function body • lib/core/providers/ai_provider.dart:275:41 • prefer_expression_function_bodies
   info • Unnecessary use of a block function body • lib/core/providers/ai_provider.dart:282:38 • prefer_expression_function_bodies
warning • Unused import: 'dart:typed_data' • lib/core/services/ai_service.dart:3:8 • unused_import
   info • The imported package 'path' isn't a dependency of the importing package • lib/core/services/ai_service.dart:5:8 • depend_on_referenced_packages
   info • Constructor declarations should be before non-constructor declarations • lib/core/services/ai_service.dart:15:3 • sort_constructors_first
   info • The method is used to change a property • lib/core/services/ai_service.dart:18:8 • use_setters_to_change_properties
  error • The argument type 'dynamic' can't be assigned to the parameter type 'Map<String, dynamic>'.  • lib/core/services/ai_service.dart:57:40 • argument_type_not_assignable
  error • The argument type 'dynamic' can't be assigned to the parameter type 'Map<String, dynamic>'.  • lib/core/services/ai_service.dart:76:42 • argument_type_not_assignable
  error • The argument type 'dynamic' can't be assigned to the parameter type 'Map<String, dynamic>'.  • lib/core/services/ai_service.dart:124:40 • argument_type_not_assignable
   info • Method invocation or property access on a 'dynamic' target • lib/core/services/ai_service.dart:127:34 • avoid_dynamic_calls
  error • The argument type 'dynamic' can't be assigned to the parameter type 'String'.  • lib/core/services/ai_service.dart:127:34 • argument_type_not_assignable
  error • The argument type 'dynamic' can't be assigned to the parameter type 'Map<String, dynamic>'.  • lib/core/services/ai_service.dart:175:40 • argument_type_not_assignable
   info • Method invocation or property access on a 'dynamic' target • lib/core/services/ai_service.dart:178:34 • avoid_dynamic_calls
  error • The argument type 'dynamic' can't be assigned to the parameter type 'String'.  • lib/core/services/ai_service.dart:178:34 • argument_type_not_assignable
  error • The argument type 'dynamic' can't be assigned to the parameter type 'Map<String, dynamic>'.  • lib/core/services/ai_service.dart:218:47 • argument_type_not_assignable
   info • Method invocation or property access on a 'dynamic' target • lib/core/services/ai_service.dart:221:34 • avoid_dynamic_calls
  error • The argument type 'dynamic' can't be assigned to the parameter type 'String'.  • lib/core/services/ai_service.dart:221:34 • argument_type_not_assignable
  error • The argument type 'dynamic' can't be assigned to the parameter type 'Map<String, dynamic>'.  • lib/core/services/ai_service.dart:238:38 • argument_type_not_assignable
   info • Method invocation or property access on a 'dynamic' target • lib/core/services/ai_service.dart:259:34 • avoid_dynamic_calls
  error • The argument type 'dynamic' can't be assigned to the parameter type 'String'.  • lib/core/services/ai_service.dart:259:34 • argument_type_not_assignable
   info • Method invocation or property access on a 'dynamic' target • lib/core/services/ai_service.dart:285:24 • avoid_dynamic_calls
  error • The argument type 'dynamic' can't be assigned to the parameter type 'Map<String, dynamic>'.  • lib/core/services/ai_service.dart:286:50 • argument_type_not_assignable
  error • A value of type 'dynamic' can't be returned from the method 'getSupportedLanguages' because it has a return type of 'Future<Map<String, dynamic>>' • lib/core/services/ai_service.dart:306:16 • return_of_invalid_type
   info • Constructor declarations should be before non-constructor declarations • lib/core/services/ai_service.dart:325:3 • sort_constructors_first
   info • Constructor declarations should be before non-constructor declarations • lib/core/services/ai_service.dart:337:3 • sort_constructors_first
   info • Constructor declarations should be before non-constructor declarations • lib/core/services/ai_service.dart:343:11 • sort_constructors_first
   info • Unnecessary use of a block function body • lib/core/services/ai_service.dart:343:62 • prefer_expression_function_bodies
  error • The argument type 'dynamic' can't be assigned to the parameter type 'String'.  • lib/core/services/ai_service.dart:345:15 • argument_type_not_assignable
  error • The argument type 'dynamic' can't be assigned to the parameter type 'Map<String, dynamic>'.  • lib/core/services/ai_service.dart:346:17 • argument_type_not_assignable
  error • The argument type 'dynamic' can't be assigned to the parameter type 'Map<String, dynamic>?'.  • lib/core/services/ai_service.dart:347:16 • argument_type_not_assignable
   info • Constructor declarations should be before non-constructor declarations • lib/core/services/ai_service.dart:361:3 • sort_constructors_first
   info • Constructor declarations should be before non-constructor declarations • lib/core/services/ai_service.dart:368:11 • sort_constructors_first
   info • Unnecessary use of a block function body • lib/core/services/ai_service.dart:368:64 • prefer_expression_function_bodies
  error • The argument type 'dynamic' can't be assigned to the parameter type 'bool'.  • lib/core/services/ai_service.dart:370:16 • argument_type_not_assignable
  error • The argument type 'dynamic' can't be assigned to the parameter type 'Map<String, dynamic>'.  • lib/core/services/ai_service.dart:371:15 • argument_type_not_assignable
  error • The argument type 'dynamic' can't be assigned to the parameter type 'Map<String, dynamic>'.  • lib/core/services/ai_service.dart:372:19 • argument_type_not_assignable
  error • The argument type 'dynamic' can't be assigned to the parameter type 'Map<String, dynamic>'.  • lib/core/services/ai_service.dart:373:17 • argument_type_not_assignable
   info • Constructor declarations should be before non-constructor declarations • lib/core/services/ai_service.dart:386:3 • sort_constructors_first
   info • Constructor declarations should be before non-constructor declarations • lib/core/services/ai_service.dart:394:11 • sort_constructors_first
   info • Unnecessary use of a block function body • lib/core/services/ai_service.dart:394:62 • prefer_expression_function_bodies
  error • The argument type 'dynamic' can't be assigned to the parameter type 'bool'.  • lib/core/services/ai_service.dart:396:16 • argument_type_not_assignable
  error • The argument type 'dynamic' can't be assigned to the parameter type 'String'.  • lib/core/services/ai_service.dart:397:16 • argument_type_not_assignable
  error • The argument type 'dynamic' can't be assigned to the parameter type 'String'.  • lib/core/services/ai_service.dart:398:15 • argument_type_not_assignable
  error • The argument type 'dynamic' can't be assigned to the parameter type 'String?'.  • lib/core/services/ai_service.dart:399:18 • argument_type_not_assignable
  error • The argument type 'dynamic' can't be assigned to the parameter type 'Map<String, dynamic>?'.  • lib/core/services/ai_service.dart:400:21 • argument_type_not_assignable
   info • Constructor declarations should be before non-constructor declarations • lib/core/services/ai_service.dart:417:3 • sort_constructors_first
   info • Constructor declarations should be before non-constructor declarations • lib/core/services/ai_service.dart:429:11 • sort_constructors_first
   info • Unnecessary use of a block function body • lib/core/services/ai_service.dart:429:60 • prefer_expression_function_bodies
  error • The argument type 'dynamic' can't be assigned to the parameter type 'String'.  • lib/core/services/ai_service.dart:431:15 • argument_type_not_assignable
  error • The argument type 'dynamic' can't be assigned to the parameter type 'String'.  • lib/core/services/ai_service.dart:432:17 • argument_type_not_assignable
  error • The argument type 'dynamic' can't be assigned to the parameter type 'String'.  • lib/core/services/ai_service.dart:433:15 • argument_type_not_assignable
  error • The argument type 'dynamic' can't be assigned to the parameter type 'Map<String, dynamic>'.  • lib/core/services/ai_service.dart:434:41 • argument_type_not_assignable
  error • The argument type 'dynamic' can't be assigned to the parameter type 'String'.  • lib/core/services/ai_service.dart:435:62 • argument_type_not_assignable
  error • The argument type 'dynamic' can't be assigned to the parameter type 'String'.  • lib/core/services/ai_service.dart:436:62 • argument_type_not_assignable
  error • The argument type 'dynamic' can't be assigned to the parameter type 'String'.  • lib/core/services/ai_service.dart:437:66 • argument_type_not_assignable
  error • The argument type 'dynamic' can't be assigned to the parameter type 'Map<String, dynamic>?'.  • lib/core/services/ai_service.dart:438:15 • argument_type_not_assignable
  error • The argument type 'dynamic' can't be assigned to the parameter type 'String?'.  • lib/core/services/ai_service.dart:439:14 • argument_type_not_assignable
   info • Constructor declarations should be before non-constructor declarations • lib/core/services/ai_service.dart:458:3 • sort_constructors_first
   info • Constructor declarations should be before non-constructor declarations • lib/core/services/ai_service.dart:466:11 • sort_constructors_first
   info • Unnecessary use of a block function body • lib/core/services/ai_service.dart:466:62 • prefer_expression_function_bodies
  error • The argument type 'dynamic' can't be assigned to the parameter type 'int'.  • lib/core/services/ai_service.dart:468:20 • argument_type_not_assignable
  error • The argument type 'dynamic' can't be assigned to the parameter type 'int'.  • lib/core/services/ai_service.dart:469:19 • argument_type_not_assignable
   info • Method invocation or property access on a 'dynamic' target • lib/core/services/ai_service.dart:470:19 • avoid_dynamic_calls
  error • The argument type 'dynamic' can't be assigned to the parameter type 'double'.  • lib/core/services/ai_service.dart:470:19 • argument_type_not_assignable
  error • The argument type 'dynamic' can't be assigned to the parameter type 'String'.  • lib/core/services/ai_service.dart:471:24 • argument_type_not_assignable
  error • The argument type 'dynamic' can't be assigned to the parameter type 'int?'.  • lib/core/services/ai_service.dart:472:31 • argument_type_not_assignable
   info • Constructor declarations should be before non-constructor declarations • lib/core/services/ai_service.dart:484:3 • sort_constructors_first
   info • Constructor declarations should be before non-constructor declarations • lib/core/services/ai_service.dart:491:11 • sort_constructors_first
   info • Unnecessary use of a block function body • lib/core/services/ai_service.dart:491:69 • prefer_expression_function_bodies
  error • The argument type 'dynamic' can't be assigned to the parameter type 'bool'.  • lib/core/services/ai_service.dart:493:16 • argument_type_not_assignable
  error • The argument type 'dynamic' can't be assigned to the parameter type 'Map<String, dynamic>'.  • lib/core/services/ai_service.dart:494:19 • argument_type_not_assignable
  error • The argument type 'dynamic' can't be assigned to the parameter type 'String'.  • lib/core/services/ai_service.dart:495:17 • argument_type_not_assignable
  error • The argument type 'dynamic' can't be assigned to the parameter type 'Map<String, dynamic>'.  • lib/core/services/ai_service.dart:496:27 • argument_type_not_assignable
  error • The argument type 'dynamic' can't be assigned to the parameter type 'Iterable<dynamic>'.  • lib/core/services/ai_service.dart:501:50 • argument_type_not_assignable
  error • The argument type 'dynamic' can't be assigned to the parameter type 'Iterable<dynamic>'.  • lib/core/services/ai_service.dart:502:57 • argument_type_not_assignable
   info • Method invocation or property access on a 'dynamic' target • lib/core/services/ai_service.dart:503:27 • avoid_dynamic_calls
  error • A value of type 'dynamic' can't be returned from the function 'duration' because it has a return type of 'double?' • lib/core/services/ai_service.dart:503:27 • return_of_invalid_type
   info • The import of 'package:flutter/services.dart' is unnecessary because all of the used elements are also provided by the import of 'package:flutter/cupertino.dart' • lib/features/memorial/ai_photo_repair_screen.dart:3:8 • unnecessary_import
   info • Sort directive sections alphabetically • lib/features/memorial/ai_photo_repair_screen.dart:5:1 • directives_ordering
   info • Use 'package:' imports for files in the 'lib' directory • lib/features/memorial/ai_photo_repair_screen.dart:6:8 • always_use_package_imports
   info • Use 'package:' imports for files in the 'lib' directory • lib/features/memorial/ai_photo_repair_screen.dart:7:8 • always_use_package_imports
warning • Unused import: '../../core/services/ai_service.dart' • lib/features/memorial/ai_photo_repair_screen.dart:7:8 • unused_import
   info • Unnecessary use of a block function body • lib/features/memorial/ai_photo_repair_screen.dart:32:38 • prefer_expression_function_bodies
   info • Unnecessary use of a block function body • lib/features/memorial/ai_photo_repair_screen.dart:81:36 • prefer_expression_function_bodies
   info • Unnecessary use of a block function body • lib/features/memorial/ai_photo_repair_screen.dart:120:34 • prefer_expression_function_bodies
   info • Closure should be a tearoff • lib/features/memorial/ai_photo_repair_screen.dart:135:30 • unnecessary_lambdas
   info • Unnecessary use of a block function body • lib/features/memorial/ai_photo_repair_screen.dart:157:32 • prefer_expression_function_bodies
   info • Unnecessary use of a block function body • lib/features/memorial/ai_photo_repair_screen.dart:207:33 • prefer_expression_function_bodies
   info • Closure should be a tearoff • lib/features/memorial/ai_photo_repair_screen.dart:226:28 • unnecessary_lambdas
   info • Unnecessary use of a block function body • lib/features/memorial/ai_photo_repair_screen.dart:240:45 • prefer_expression_function_bodies
   info • Unnecessary use of a block function body • lib/features/memorial/ai_photo_repair_screen.dart:288:33 • prefer_expression_function_bodies
   info • Unnecessary type annotation on a local variable • lib/features/memorial/ai_photo_repair_screen.dart:353:15 • omit_local_variable_types
   info • Unnecessary type annotation on a local variable • lib/features/memorial/ai_photo_repair_screen.dart:361:15 • omit_local_variable_types
   info • Catch clause should use 'on' to specify the type of exception being caught • lib/features/memorial/ai_photo_repair_screen.dart:368:7 • avoid_catches_without_on_clauses
   info • Catch clause should use 'on' to specify the type of exception being caught • lib/features/memorial/ai_photo_repair_screen.dart:444:7 • avoid_catches_without_on_clauses
   info • Use 'package:' imports for files in the 'lib' directory • lib/features/memorial/ai_services_screen.dart:3:8 • always_use_package_imports
   info • Use 'package:' imports for files in the 'lib' directory • lib/features/memorial/ai_services_screen.dart:4:8 • always_use_package_imports
   info • Use 'package:' imports for files in the 'lib' directory • lib/features/memorial/ai_services_screen.dart:5:8 • always_use_package_imports
   info • Sort directive sections alphabetically • lib/features/memorial/ai_services_screen.dart:6:1 • directives_ordering
   info • Use 'package:' imports for files in the 'lib' directory • lib/features/memorial/ai_services_screen.dart:6:8 • always_use_package_imports
   info • Unnecessary use of a block function body • lib/features/memorial/ai_services_screen.dart:26:38 • prefer_expression_function_bodies
   info • Unnecessary use of a block function body • lib/features/memorial/ai_services_screen.dart:32:47 • prefer_expression_function_bodies
   info • The value of the argument is redundant because it matches the default value • lib/features/memorial/ai_services_screen.dart:70:18 • avoid_redundant_argument_values
   info • Unnecessary use of a block function body • lib/features/memorial/ai_services_screen.dart:120:74 • prefer_expression_function_bodies
warning • The type argument(s) of the constructor 'CupertinoPageRoute' can't be inferred • lib/features/memorial/ai_services_screen.dart:155:21 • inference_failure_on_instance_creation
warning • The type argument(s) of the constructor 'CupertinoPageRoute' can't be inferred • lib/features/memorial/ai_services_screen.dart:178:21 • inference_failure_on_instance_creation
warning • The type argument(s) of the constructor 'CupertinoPageRoute' can't be inferred • lib/features/memorial/ai_services_screen.dart:214:21 • inference_failure_on_instance_creation
warning • The type argument(s) of the constructor 'CupertinoPageRoute' can't be inferred • lib/features/memorial/ai_services_screen.dart:236:21 • inference_failure_on_instance_creation
warning • The type argument(s) of the constructor 'CupertinoPageRoute' can't be inferred • lib/features/memorial/ai_services_screen.dart:254:21 • inference_failure_on_instance_creation
   info • Use 'package:' imports for files in the 'lib' directory • lib/features/memorial/ai_task_list_screen.dart:3:8 • always_use_package_imports
   info • Use 'package:' imports for files in the 'lib' directory • lib/features/memorial/ai_task_list_screen.dart:4:8 • always_use_package_imports
   info • Constructor declarations should be before non-constructor declarations • lib/features/memorial/ai_task_list_screen.dart:9:9 • sort_constructors_first
warning • The value of the field '_filterOptions' isn't used • lib/features/memorial/ai_task_list_screen.dart:21:29 • unused_field
   info • Unnecessary use of a block function body • lib/features/memorial/ai_task_list_screen.dart:42:38 • prefer_expression_function_bodies
   info • Unnecessary use of a block function body • lib/features/memorial/ai_task_list_screen.dart:53:47 • prefer_expression_function_bodies
   info • Unnecessary use of a block function body • lib/features/memorial/ai_task_list_screen.dart:75:33 • prefer_expression_function_bodies
   info • Use 'const' literals as arguments to constructors of '@immutable' classes • lib/features/memorial/ai_task_list_screen.dart:80:19 • prefer_const_literals_to_create_immutables
   info • Unnecessary use of a block function body • lib/features/memorial/ai_task_list_screen.dart:142:66 • prefer_expression_function_bodies
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/memorial/ai_task_list_screen.dart:146:22 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/memorial/ai_task_list_screen.dart:148:41 • deprecated_member_use
   info • Unnecessary 'break' statement • lib/features/memorial/ai_task_list_screen.dart:341:9 • unnecessary_breaks
   info • Unnecessary 'break' statement • lib/features/memorial/ai_task_list_screen.dart:345:9 • unnecessary_breaks
   info • Unnecessary 'break' statement • lib/features/memorial/ai_task_list_screen.dart:349:9 • unnecessary_breaks
   info • Invalid use of 'default' member in a switch • lib/features/memorial/ai_task_list_screen.dart:350:7 • no_default_cases
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/memorial/ai_task_list_screen.dart:358:22 • deprecated_member_use
   info • Unnecessary 'break' statement • lib/features/memorial/ai_task_list_screen.dart:373:9 • unnecessary_breaks
   info • Unnecessary 'break' statement • lib/features/memorial/ai_task_list_screen.dart:377:9 • unnecessary_breaks
   info • Unnecessary 'break' statement • lib/features/memorial/ai_task_list_screen.dart:381:9 • unnecessary_breaks
   info • Unnecessary 'break' statement • lib/features/memorial/ai_task_list_screen.dart:385:9 • unnecessary_breaks
   info • Unnecessary 'break' statement • lib/features/memorial/ai_task_list_screen.dart:389:9 • unnecessary_breaks
   info • Invalid use of 'default' member in a switch • lib/features/memorial/ai_task_list_screen.dart:390:7 • no_default_cases
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/memorial/ai_task_list_screen.dart:398:22 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/features/memorial/ai_task_list_screen.dart:400:41 • deprecated_member_use
   info • Unnecessary use of a block function body • lib/features/memorial/ai_task_list_screen.dart:413:53 • prefer_expression_function_bodies
   info • Unnecessary use of a block function body • lib/features/memorial/ai_task_list_screen.dart:498:45 • prefer_expression_function_bodies
   info • The import of 'package:flutter/services.dart' is unnecessary because all of the used elements are also provided by the import of 'package:flutter/cupertino.dart' • lib/features/memorial/ai_voice_cloning_screen.dart:3:8 • unnecessary_import
   info • Sort directive sections alphabetically • lib/features/memorial/ai_voice_cloning_screen.dart:5:1 • directives_ordering
   info • Use 'package:' imports for files in the 'lib' directory • lib/features/memorial/ai_voice_cloning_screen.dart:6:8 • always_use_package_imports
   info • Use 'package:' imports for files in the 'lib' directory • lib/features/memorial/ai_voice_cloning_screen.dart:7:8 • always_use_package_imports
   info • Unnecessary use of a 'double' literal • lib/features/memorial/ai_voice_cloning_screen.dart:21:19 • prefer_int_literals
   info • Unnecessary use of a 'double' literal • lib/features/memorial/ai_voice_cloning_screen.dart:22:24 • prefer_int_literals
   info • Unnecessary use of a block function body • lib/features/memorial/ai_voice_cloning_screen.dart:42:38 • prefer_expression_function_bodies
   info • Unnecessary use of a block function body • lib/features/memorial/ai_voice_cloning_screen.dart:95:36 • prefer_expression_function_bodies
   info • The value of the argument is redundant because it matches the default value • lib/features/memorial/ai_voice_cloning_screen.dart:118:28 • avoid_redundant_argument_values
   info • Unnecessary use of a block function body • lib/features/memorial/ai_voice_cloning_screen.dart:283:28 • prefer_expression_function_bodies
   info • Unnecessary use of a block function body • lib/features/memorial/ai_voice_cloning_screen.dart:333:35 • prefer_expression_function_bodies
   info • Unnecessary use of a block function body • lib/features/memorial/ai_voice_cloning_screen.dart:359:35 • prefer_expression_function_bodies
   info • Unnecessary use of a 'double' literal • lib/features/memorial/ai_voice_cloning_screen.dart:384:22 • prefer_int_literals
   info • Unnecessary use of a 'double' literal • lib/features/memorial/ai_voice_cloning_screen.dart:401:23 • prefer_int_literals
   info • Unnecessary use of a 'double' literal • lib/features/memorial/ai_voice_cloning_screen.dart:402:22 • prefer_int_literals
   info • Unnecessary use of a block function body • lib/features/memorial/ai_voice_cloning_screen.dart:417:28 • prefer_expression_function_bodies
   info • Unnecessary use of a block function body • lib/features/memorial/ai_voice_cloning_screen.dart:460:22 • prefer_expression_function_bodies
   info • Unnecessary use of a block function body • lib/features/memorial/ai_voice_cloning_screen.dart:466:25 • prefer_expression_function_bodies
   info • The value of the argument is redundant because it matches the default value • lib/features/memorial/ai_voice_cloning_screen.dart:474:24 • avoid_redundant_argument_values
   info • Catch clause should use 'on' to specify the type of exception being caught • lib/features/memorial/ai_voice_cloning_screen.dart:483:7 • avoid_catches_without_on_clauses
   info • Statement should be on a separate line • lib/features/memorial/ai_voice_cloning_screen.dart:489:37 • always_put_control_body_on_new_line
   info • Catch clause should use 'on' to specify the type of exception being caught • lib/features/memorial/ai_voice_cloning_screen.dart:501:7 • avoid_catches_without_on_clauses
   info • To-do comment doesn't follow the Flutter style • lib/features/memorial/ai_voice_cloning_screen.dart:507:5 • flutter_style_todos
   info • Closure should be a tearoff • lib/features/memorial/ai_voice_cloning_screen.dart:544:49 • unnecessary_lambdas
   info • Statement should be on a separate line • lib/features/memorial/ai_voice_cloning_screen.dart:554:25 • always_put_control_body_on_new_line
   info • Catch clause should use 'on' to specify the type of exception being caught • lib/features/memorial/ai_voice_cloning_screen.dart:576:7 • avoid_catches_without_on_clauses
   info • Use 'package:' imports for files in the 'lib' directory • lib/main.dart:4:8 • always_use_package_imports
   info • Sort directive sections alphabetically • lib/main.dart:5:1 • directives_ordering
   info • Use 'package:' imports for files in the 'lib' directory • lib/main.dart:5:8 • always_use_package_imports
   info • Place 'package:' imports before relative imports • lib/main.dart:7:1 • directives_ordering
   info • Place 'package:' imports before relative imports • lib/main.dart:8:1 • directives_ordering
   info • Place 'package:' imports before relative imports • lib/main.dart:9:1 • directives_ordering
   info • Place 'package:' imports before relative imports • lib/main.dart:10:1 • directives_ordering
   info • Place 'package:' imports before relative imports • lib/main.dart:11:1 • directives_ordering
   info • Place 'package:' imports before relative imports • lib/main.dart:12:1 • directives_ordering
   info • Place 'package:' imports before relative imports • lib/main.dart:13:1 • directives_ordering
   info • Place 'package:' imports before relative imports • lib/main.dart:14:1 • directives_ordering
   info • Place 'package:' imports before relative imports • lib/main.dart:15:1 • directives_ordering
   info • Place 'package:' imports before relative imports • lib/main.dart:16:1 • directives_ordering
   info • Place 'package:' imports before relative imports • lib/main.dart:17:1 • directives_ordering
   info • Place 'package:' imports before relative imports • lib/main.dart:18:1 • directives_ordering
   info • Place 'package:' imports before relative imports • lib/main.dart:19:1 • directives_ordering
   info • Place 'package:' imports before relative imports • lib/main.dart:20:1 • directives_ordering
   info • Place 'package:' imports before relative imports • lib/main.dart:21:1 • directives_ordering
   info • Use 'package:' imports for files in the 'lib' directory • lib/providers/service_providers.dart:3:8 • always_use_package_imports
   info • Sort directive sections alphabetically • lib/providers/service_providers.dart:4:1 • directives_ordering
   info • Use 'package:' imports for files in the 'lib' directory • lib/providers/service_providers.dart:4:8 • always_use_package_imports
   info • Sort directive sections alphabetically • test/ai_services_test.dart:5:1 • directives_ordering
   info • Sort directive sections alphabetically • test/ai_services_test.dart:6:1 • directives_ordering
   info • Sort directive sections alphabetically • test/ai_services_test.dart:9:1 • directives_ordering
   info • Sort directive sections alphabetically • test/ai_services_test.dart:10:1 • directives_ordering
   info • Sort directive sections alphabetically • test/ai_services_test.dart:12:1 • directives_ordering
   info • Sort directive sections alphabetically • test/ai_services_test.dart:14:1 • directives_ordering
  error • Target of URI doesn't exist: 'ai_services_test.mocks.dart' • test/ai_services_test.dart:18:8 • uri_does_not_exist
  error • Undefined class 'MockClient' • test/ai_services_test.dart:22:10 • undefined_class
  error • Undefined class 'MockAIService' • test/ai_services_test.dart:23:10 • undefined_class
  error • The function 'MockClient' isn't defined • test/ai_services_test.dart:27:20 • undefined_function
  error • The function 'MockAIService' isn't defined • test/ai_services_test.dart:28:23 • undefined_function
   info • Closure should be a tearoff • test/ai_services_test.dart:105:11 • unnecessary_lambdas
   info • The value of the argument is redundant because it matches the default value • test/ai_services_test.dart:138:21 • avoid_redundant_argument_values
warning • The type argument(s) of 'Map' can't be inferred • test/ai_services_test.dart:163:52 • inference_failure_on_collection_literal
   info • Unnecessary use of a 'double' literal • test/ai_services_test.dart:426:27 • prefer_int_literals
   info • Unnecessary type annotation on a local variable • test/ai_services_test.dart:458:14 • omit_local_variable_types

211 issues found. (ran in 1.1s)
