#!/bin/bash

# Memorial 项目开发环境快速启动脚本
# 专为开发者优化的简化版本

set -e

# 切换到项目根目录
cd "$(dirname "$0")"

# 颜色输出
info() { echo -e "\033[1;34m[INFO]\033[0m $1"; }
success() { echo -e "\033[1;32m[✓]\033[0m $1"; }
error() { echo -e "\033[1;31m[✗]\033[0m $1"; }

# 配置
BACKEND_PORT=8008
FRONTEND_PORT=4001

info "🚀 Memorial 开发环境快速启动"

# 检查基础依赖
if ! command -v python3 &> /dev/null; then
    error "需要 Python 3.11+"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    error "需要 Node.js 18+"
    exit 1
fi

# 加载环境变量
if [[ -f ".env" ]]; then
    export $(grep -v '^#' .env | xargs)
    success "已加载 .env 配置"
fi

# 设置环境变量
export PYTHONPATH="${PYTHONPATH}:$(pwd)/backend"
export VITE_APP_API_BASE_URL="http://localhost:${BACKEND_PORT}/api/v1"
export VITE_APP_BACKEND_URL="http://localhost:${BACKEND_PORT}"

# 激活 conda 环境
if command -v conda &> /dev/null; then
    source "$(conda info --base)/etc/profile.d/conda.sh"
    conda activate memorial 2>/dev/null || info "使用系统 Python"
fi

# 启动后端
info "启动后端服务..."
cd backend
if ! python3 -c "import fastapi" &> /dev/null; then
    info "安装后端依赖..."
    pip install -r requirements.txt
fi

uvicorn app.main:app --host 0.0.0.0 --port ${BACKEND_PORT} --reload &
BACKEND_PID=$!
cd ..

# 启动前端
info "启动前端服务..."
cd frontend
if [[ ! -d "node_modules" ]]; then
    info "安装前端依赖..."
    npm install
fi

npm run dev -- --port ${FRONTEND_PORT} &
FRONTEND_PID=$!
cd ..

# 等待服务启动
sleep 3

# 检查服务状态
if kill -0 $BACKEND_PID 2>/dev/null && kill -0 $FRONTEND_PID 2>/dev/null; then
    success "🎉 服务启动成功！"
    echo ""
    echo "📍 访问地址:"
    echo "  🌐 前端: http://localhost:${FRONTEND_PORT}"
    echo "  🔧 后端: http://localhost:${BACKEND_PORT}"
    echo "  📚 API文档: http://localhost:${BACKEND_PORT}/docs"
    echo ""
    echo "💡 按 Ctrl+C 停止服务"
else
    error "服务启动失败"
    kill $BACKEND_PID $FRONTEND_PID 2>/dev/null || true
    exit 1
fi

# 优雅关闭
cleanup() {
    echo ""
    info "停止服务..."
    kill $BACKEND_PID $FRONTEND_PID 2>/dev/null || true
    success "服务已停止"
    exit 0
}

trap cleanup INT TERM

# 保持运行
while true; do
    if ! kill -0 $BACKEND_PID 2>/dev/null || ! kill -0 $FRONTEND_PID 2>/dev/null; then
        error "服务异常停止"
        cleanup
    fi
    sleep 5
done
