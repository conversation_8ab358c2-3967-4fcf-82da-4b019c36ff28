# Memorial 项目启动脚本最佳实践指南

## 📋 概述

本文档描述了 Memorial 项目的启动脚本最佳实践，包括统一的端口配置、环境管理、依赖检查和服务监控。

## 🚀 启动脚本说明

### 1. 主要启动脚本

#### `start_memorial.sh` - 推荐使用 ⭐
- **功能**: 完整的生产级启动脚本
- **特点**: 支持开发/生产模式切换，完整的错误处理和监控
- **使用方法**:
  ```bash
  # 开发模式启动所有服务
  ./start_memorial.sh --dev
  
  # 生产模式启动所有服务
  ./start_memorial.sh --prod
  
  # 仅启动后端
  ./start_memorial.sh --backend-only
  
  # 仅启动前端
  ./start_memorial.sh --frontend-only
  ```

#### `dev-start.sh` - 开发快速启动
- **功能**: 简化的开发环境启动脚本
- **特点**: 快速启动，适合日常开发
- **使用方法**:
  ```bash
  ./dev-start.sh
  ```

#### `start_server.sh` - 兼容性脚本
- **功能**: 原有的启动脚本，保持向后兼容
- **特点**: 基于 FastAPI + Vite 的标准启动流程

### 2. 服务管理脚本

#### `stop_server.sh` - 停止服务
- **功能**: 优雅停止所有 Memorial 相关服务
- **特点**: 智能检测端口占用，强制停止顽固进程

#### `health-check.sh` - 健康检查
- **功能**: 全面的系统和服务健康检查
- **特点**: 检查依赖、环境、数据库、服务状态

## 🔧 配置标准化

### 端口配置
- **后端端口**: 8008 (统一配置)
- **前端端口**: 4001 (统一配置)
- **数据库端口**: 5432 (PostgreSQL 默认)

### 环境变量管理
1. **配置文件**: `.env` (从 `.env.example` 复制)
2. **关键变量**:
   ```bash
   # 服务配置
   SERVER_HOST=http://localhost:8008
   FRONTEND_HOST=http://localhost:4001
   
   # 数据库配置
   DATABASE_URL=postgresql://memorial:memorial@localhost/memorial
   
   # 安全配置
   SECRET_KEY=your-secret-key
   JWT_SECRET_KEY=your-jwt-secret
   
   # AI 服务
   REPLICATE_API_TOKEN=your-token
   ```

## 📦 依赖管理最佳实践

### 后端依赖
1. **主要依赖**: 通过 `environment_clean.yml` (conda)
2. **特殊依赖**: 通过 `requirements.txt` (pip)
3. **检查命令**:
   ```bash
   # 检查核心依赖
   python3 -c "import fastapi, uvicorn, sqlalchemy"
   
   # 安装依赖
   pip install -r requirements.txt
   ```

### 前端依赖
1. **包管理器**: npm (统一使用)
2. **依赖文件**: `package.json`
3. **检查命令**:
   ```bash
   # 检查依赖
   [[ -d "node_modules" ]] && echo "依赖已安装"
   
   # 安装依赖
   npm install
   ```

## 🔍 监控和健康检查

### 自动健康检查
启动脚本包含以下检查：
1. **依赖检查**: Python, Node.js, npm
2. **环境检查**: .env 文件，关键环境变量
3. **服务检查**: 端点响应，HTTP 状态码
4. **数据库检查**: PostgreSQL 连接

### 手动健康检查
```bash
# 运行完整健康检查
./health-check.sh

# 检查特定服务
curl http://localhost:8008/health
curl http://localhost:4001
```

## 🛡️ 安全最佳实践

### 1. 环境变量安全
- ✅ 使用 `.env` 文件管理敏感信息
- ✅ 提供 `.env.example` 模板
- ✅ 在生产环境中使用强密钥
- ❌ 不要在代码中硬编码敏感信息

### 2. 端口安全
- ✅ 使用非标准端口避免冲突
- ✅ 在生产环境中配置防火墙
- ✅ 使用 HTTPS (生产环境)

### 3. 进程管理
- ✅ 优雅关闭进程 (SIGTERM)
- ✅ 强制停止顽固进程 (SIGKILL)
- ✅ 监控进程状态

## 🚨 故障排除

### 常见问题

#### 1. 端口被占用
```bash
# 查看端口占用
lsof -i :8008
lsof -i :4001

# 停止占用进程
./stop_server.sh
```

#### 2. 依赖缺失
```bash
# 后端依赖
pip install -r backend/requirements.txt

# 前端依赖
cd frontend && npm install
```

#### 3. 数据库连接失败
```bash
# 检查 PostgreSQL 服务
brew services list | grep postgresql

# 启动 PostgreSQL
brew services start postgresql
```

#### 4. 环境变量问题
```bash
# 检查 .env 文件
ls -la .env

# 复制模板
cp .env.example .env
```

### 调试模式
```bash
# 启用详细日志
export DEBUG=true

# 查看服务日志
tail -f backend/logs/error.log
```

## 📈 性能优化

### 开发环境
- 使用 `--reload` 模式自动重启
- 启用热更新 (Vite HMR)
- 使用开发服务器 (Uvicorn)

### 生产环境
- 使用 Gunicorn + 多进程
- 启用静态文件缓存
- 配置反向代理 (Nginx)

## 🔄 CI/CD 集成

### GitHub Actions 示例
```yaml
name: Health Check
on: [push, pull_request]
jobs:
  health-check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Environment
        run: cp .env.example .env
      - name: Start Services
        run: ./start_memorial.sh --dev &
      - name: Health Check
        run: ./health-check.sh
```

## 📚 相关文档

- [环境配置指南](./环境配置指南.md)
- [数据库配置](./数据库配置.md)
- [API 文档](http://localhost:8008/docs)
- [项目结构](../README.md)

## 🤝 贡献指南

1. 修改启动脚本时，确保向后兼容
2. 添加新的环境变量时，更新 `.env.example`
3. 测试所有启动模式 (dev/prod, backend-only/frontend-only)
4. 更新相关文档

---

**最后更新**: 2025-06-16
**维护者**: Memorial 开发团队
