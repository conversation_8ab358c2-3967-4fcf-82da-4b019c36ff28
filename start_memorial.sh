#!/bin/bash

# Memorial 项目统一启动脚本 - 最佳实践版本
# 使用方法: ./start_memorial.sh [--dev|--prod] [--backend-only|--frontend-only]

set -e  # 遇到错误时退出

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 默认配置
MODE="dev"
SERVICE="all"
BACKEND_PORT=8008
FRONTEND_PORT=4001

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --dev)
            MODE="dev"
            shift
            ;;
        --prod)
            MODE="prod"
            shift
            ;;
        --backend-only)
            SERVICE="backend"
            shift
            ;;
        --frontend-only)
            SERVICE="frontend"
            shift
            ;;
        -h|--help)
            echo "使用方法: $0 [选项]"
            echo "选项:"
            echo "  --dev              开发模式 (默认)"
            echo "  --prod             生产模式"
            echo "  --backend-only     仅启动后端"
            echo "  --frontend-only    仅启动前端"
            echo "  -h, --help         显示帮助信息"
            exit 0
            ;;
        *)
            echo "未知选项: $1"
            echo "使用 $0 --help 查看帮助"
            exit 1
            ;;
    esac
done

# 颜色输出函数
print_info() { echo -e "\033[1;34m[INFO]\033[0m $1"; }
print_success() { echo -e "\033[1;32m[SUCCESS]\033[0m $1"; }
print_warning() { echo -e "\033[1;33m[WARNING]\033[0m $1"; }
print_error() { echo -e "\033[1;31m[ERROR]\033[0m $1"; }

# 检查依赖函数
check_dependencies() {
    print_info "检查系统依赖..."
    
    # 检查 Python
    if ! command -v python3 &> /dev/null; then
        print_error "未找到 Python3，请先安装 Python 3.11+"
        exit 1
    fi
    
    # 检查 Node.js
    if [[ "$SERVICE" == "all" || "$SERVICE" == "frontend" ]]; then
        if ! command -v npm &> /dev/null; then
            print_error "未找到 npm，请先安装 Node.js 18+"
            exit 1
        fi
    fi
    
    # 检查 PostgreSQL (可选)
    if ! command -v psql &> /dev/null; then
        print_warning "未找到 PostgreSQL 客户端，请确保数据库服务正在运行"
    fi
    
    print_success "系统依赖检查完成"
}

# 环境设置函数
setup_environment() {
    print_info "设置环境变量..."
    
    # 加载 .env 文件
    if [[ -f ".env" ]]; then
        export $(grep -v '^#' .env | xargs)
        print_success "已加载 .env 文件"
    else
        print_warning "未找到 .env 文件，使用默认配置"
        print_info "建议复制 .env.example 为 .env 并配置相应值"
    fi
    
    # 设置基础环境变量
    export PYTHONPATH="${PYTHONPATH}:${SCRIPT_DIR}/backend"
    export DATABASE_URL="${DATABASE_URL:-postgresql://memorial:memorial@localhost/memorial}"
    export SECRET_KEY="${SECRET_KEY:-memorial-dev-secret-key-2025}"
    export JWT_SECRET_KEY="${JWT_SECRET_KEY:-memorial-jwt-secret-key-2025}"
    export REPLICATE_API_TOKEN="${REPLICATE_API_TOKEN:-demo-token}"
    
    # 前端环境变量
    export VITE_APP_API_BASE_URL="http://localhost:${BACKEND_PORT}/api/v1"
    export VITE_APP_BACKEND_URL="http://localhost:${BACKEND_PORT}"
    
    print_success "环境变量设置完成"
}

# 激活 conda 环境
activate_conda() {
    if command -v conda &> /dev/null; then
        print_info "激活 conda 环境: memorial"
        source "$(conda info --base)/etc/profile.d/conda.sh"
        if conda activate memorial 2>/dev/null; then
            print_success "conda 环境激活成功"
        else
            print_warning "conda 环境激活失败，继续使用系统 Python"
        fi
    else
        print_info "未找到 conda，使用系统 Python 环境"
    fi
}

# 检查并安装后端依赖
setup_backend_deps() {
    print_info "检查后端依赖..."
    cd "$SCRIPT_DIR/backend"
    
    # 检查核心依赖
    if ! python3 -c "import fastapi, uvicorn, sqlalchemy" &> /dev/null; then
        print_info "安装后端依赖..."
        pip install -r requirements.txt
        print_success "后端依赖安装完成"
    else
        print_success "后端依赖已就绪"
    fi
    
    cd "$SCRIPT_DIR"
}

# 检查并安装前端依赖
setup_frontend_deps() {
    print_info "检查前端依赖..."
    cd "$SCRIPT_DIR/frontend"
    
    if [[ ! -d "node_modules" ]] || [[ ! -f "node_modules/.package-lock.json" ]]; then
        print_info "安装前端依赖..."
        npm install
        print_success "前端依赖安装完成"
    else
        print_success "前端依赖已就绪"
    fi
    
    cd "$SCRIPT_DIR"
}

# 启动后端服务
start_backend() {
    print_info "启动后端服务 (端口: ${BACKEND_PORT})..."
    cd "$SCRIPT_DIR/backend"
    
    if [[ "$MODE" == "prod" ]]; then
        # 生产模式使用 Gunicorn
        print_info "使用 Gunicorn 启动生产环境后端..."
        gunicorn -c gunicorn.conf.py app.main:app &
    else
        # 开发模式使用 Uvicorn
        print_info "使用 Uvicorn 启动开发环境后端..."
        uvicorn app.main:app --host 0.0.0.0 --port ${BACKEND_PORT} --reload --log-level info &
    fi
    
    BACKEND_PID=$!
    cd "$SCRIPT_DIR"
    
    # 等待后端启动
    print_info "等待后端服务启动..."
    for i in {1..30}; do
        if curl -s "http://localhost:${BACKEND_PORT}/health" > /dev/null 2>&1; then
            print_success "后端服务启动成功"
            return 0
        fi
        if [[ $i -eq 30 ]]; then
            print_error "后端服务启动超时"
            kill $BACKEND_PID 2>/dev/null || true
            return 1
        fi
        sleep 1
    done
}

# 启动前端服务
start_frontend() {
    print_info "启动前端服务 (端口: ${FRONTEND_PORT})..."
    cd "$SCRIPT_DIR/frontend"
    
    if [[ "$MODE" == "prod" ]]; then
        # 生产模式先构建再预览
        print_info "构建前端生产版本..."
        npm run build
        npm run preview -- --port ${FRONTEND_PORT} &
    else
        # 开发模式
        npm run dev -- --port ${FRONTEND_PORT} &
    fi
    
    FRONTEND_PID=$!
    cd "$SCRIPT_DIR"
    
    # 等待前端启动
    print_info "等待前端服务启动..."
    for i in {1..20}; do
        if curl -s "http://localhost:${FRONTEND_PORT}" > /dev/null 2>&1; then
            print_success "前端服务启动成功"
            return 0
        fi
        if [[ $i -eq 20 ]]; then
            print_error "前端服务启动超时"
            kill $FRONTEND_PID 2>/dev/null || true
            return 1
        fi
        sleep 1
    done
}

# 显示服务信息
show_service_info() {
    echo ""
    print_success "🎉 Memorial 项目启动完成！"
    echo ""
    echo "📍 服务地址:"
    
    if [[ "$SERVICE" == "all" || "$SERVICE" == "frontend" ]]; then
        echo "  🌐 前端应用: http://localhost:${FRONTEND_PORT}"
    fi
    
    if [[ "$SERVICE" == "all" || "$SERVICE" == "backend" ]]; then
        echo "  🔧 后端 API: http://localhost:${BACKEND_PORT}"
        echo "  📚 API 文档: http://localhost:${BACKEND_PORT}/docs"
        echo "  🔍 健康检查: http://localhost:${BACKEND_PORT}/health"
    fi
    
    echo ""
    echo "🛠️  运行模式: $MODE"
    echo "💡 按 Ctrl+C 停止所有服务"
    echo ""
}

# 优雅关闭处理
cleanup() {
    echo ""
    print_info "正在停止服务..."
    
    if [[ ! -z "${BACKEND_PID:-}" ]]; then
        kill $BACKEND_PID 2>/dev/null && print_success "后端服务已停止"
    fi
    
    if [[ ! -z "${FRONTEND_PID:-}" ]]; then
        kill $FRONTEND_PID 2>/dev/null && print_success "前端服务已停止"
    fi
    
    print_success "所有服务已停止，再见！"
    exit 0
}

# 主函数
main() {
    print_info "🚀 启动 Memorial 项目 (模式: $MODE, 服务: $SERVICE)..."
    
    # 设置信号处理
    trap cleanup INT TERM
    
    # 执行启动流程
    check_dependencies
    setup_environment
    activate_conda
    
    # 根据服务类型启动
    case $SERVICE in
        "backend")
            setup_backend_deps
            start_backend
            ;;
        "frontend")
            setup_frontend_deps
            start_frontend
            ;;
        "all")
            setup_backend_deps
            setup_frontend_deps
            start_backend
            start_frontend
            ;;
    esac
    
    show_service_info
    
    # 监控服务状态
    while true; do
        if [[ "$SERVICE" == "all" || "$SERVICE" == "backend" ]]; then
            if ! kill -0 ${BACKEND_PID:-0} 2>/dev/null; then
                print_error "后端服务意外停止"
                [[ ! -z "${FRONTEND_PID:-}" ]] && kill $FRONTEND_PID 2>/dev/null
                exit 1
            fi
        fi
        
        if [[ "$SERVICE" == "all" || "$SERVICE" == "frontend" ]]; then
            if ! kill -0 ${FRONTEND_PID:-0} 2>/dev/null; then
                print_error "前端服务意外停止"
                [[ ! -z "${BACKEND_PID:-}" ]] && kill $BACKEND_PID 2>/dev/null
                exit 1
            fi
        fi
        
        sleep 5
    done
}

# 执行主函数
main "$@"
