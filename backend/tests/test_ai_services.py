"""
AI服务集成测试套件
测试所有AI功能的完整性和性能
"""

import pytest
import asyncio
import tempfile
import os
from pathlib import Path
from unittest.mock import Mock, patch
from fastapi.testclient import TestClient
from PIL import Image
import io

from app.main import app
from app.ai_services.ai_task_manager import task_manager, TaskType, TaskStatus
from app.ai_services.batch_processor import BatchProcessor
from app.ai_services.voice_service import MultiLanguageVoiceCloner
from app.ai_services.replicate_service import ReplicateService


class TestAIServices:
    """AI服务测试类"""
    
    @pytest.fixture
    def client(self):
        """测试客户端"""
        return TestClient(app)
    
    @pytest.fixture
    def mock_user_token(self):
        """模拟用户认证令牌"""
        return "test_token_123"
    
    @pytest.fixture
    def test_image(self):
        """创建测试图片"""
        img = Image.new('RGB', (100, 100), color='red')
        img_bytes = io.BytesIO()
        img.save(img_bytes, format='JPEG')
        img_bytes.seek(0)
        return img_bytes
    
    @pytest.fixture
    def test_audio(self):
        """创建测试音频文件"""
        # 创建一个简单的WAV文件
        with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as f:
            # 写入简单的WAV头部和数据
            f.write(b'RIFF')
            f.write((36).to_bytes(4, 'little'))
            f.write(b'WAVE')
            f.write(b'fmt ')
            f.write((16).to_bytes(4, 'little'))
            f.write((1).to_bytes(2, 'little'))  # PCM
            f.write((1).to_bytes(2, 'little'))  # 单声道
            f.write((22050).to_bytes(4, 'little'))  # 采样率
            f.write((44100).to_bytes(4, 'little'))  # 字节率
            f.write((2).to_bytes(2, 'little'))  # 块对齐
            f.write((16).to_bytes(2, 'little'))  # 位深度
            f.write(b'data')
            f.write((0).to_bytes(4, 'little'))
            return f.name

    def test_health_check(self, client):
        """测试健康检查API"""
        response = client.get("/api/v1/ai-enhanced/health")
        assert response.status_code == 200
        
        data = response.json()
        assert "status" in data
        assert "services" in data
        assert "timestamp" in data

    def test_get_models(self, client):
        """测试获取模型信息API"""
        response = client.get("/api/v1/ai-enhanced/models")
        assert response.status_code == 200
        
        data = response.json()
        assert data["success"] is True
        assert "models" in data
        assert "statistics" in data
        assert "features" in data
        
        # 验证模型信息完整性
        models = data["models"]
        assert "photo_restoration" in models
        assert "voice_cloning" in models
        
        # 验证功能特性
        features = data["features"]
        assert features["batch_processing"] is True
        assert features["progress_tracking"] is True

    @patch('app.ai_services.replicate_service.ReplicateService')
    def test_batch_photo_process(self, mock_replicate, client, test_image, mock_user_token):
        """测试批量照片处理API"""
        # 模拟认证
        headers = {"Authorization": f"Bearer {mock_user_token}"}
        
        # 准备测试数据
        files = [
            ("files", ("test1.jpg", test_image, "image/jpeg")),
            ("files", ("test2.jpg", test_image, "image/jpeg"))
        ]
        data = {
            "operation": "restore",
            "scale": "4",
            "face_enhance": "true"
        }
        
        response = client.post(
            "/api/v1/ai-enhanced/batch/photo-process",
            files=files,
            data=data,
            headers=headers
        )
        
        assert response.status_code == 200
        result = response.json()
        assert result["success"] is True
        assert "task_id" in result
        assert "optimization" in result

    @patch('app.ai_services.replicate_service.ReplicateService')
    def test_voice_clone_enhanced(self, mock_replicate, client, test_audio, mock_user_token):
        """测试增强语音克隆API"""
        headers = {"Authorization": f"Bearer {mock_user_token}"}
        
        with open(test_audio, 'rb') as audio_file:
            files = [("voice_file", ("test.wav", audio_file, "audio/wav"))]
            data = {
                "text": "这是一个测试文本",
                "language": "zh",
                "enhance_quality": "true",
                "speed": "1.0",
                "pitch_shift": "0.0"
            }
            
            response = client.post(
                "/api/v1/ai-enhanced/voice/clone-enhanced",
                files=files,
                data=data,
                headers=headers
            )
        
        assert response.status_code == 200
        result = response.json()
        assert result["success"] is True
        assert "task_id" in result

    def test_voice_validate_sample(self, client, test_audio, mock_user_token):
        """测试语音样本验证API"""
        headers = {"Authorization": f"Bearer {mock_user_token}"}
        
        with open(test_audio, 'rb') as audio_file:
            files = [("file", ("test.wav", audio_file, "audio/wav"))]
            data = {"language": "zh"}
            
            response = client.post(
                "/api/v1/ai-enhanced/voice/validate-sample",
                files=files,
                data=data,
                headers=headers
            )
        
        assert response.status_code == 200
        result = response.json()
        assert result["success"] is True
        assert "validation" in result

    def test_get_supported_languages(self, client):
        """测试获取支持语言API"""
        response = client.get("/api/v1/ai-enhanced/voice/languages")
        assert response.status_code == 200
        
        data = response.json()
        assert data["success"] is True
        assert "languages" in data
        assert "total_languages" in data
        
        # 验证支持的语言
        languages = data["languages"]
        assert "zh" in languages
        assert "en" in languages
        assert len(languages) >= 6

    @pytest.mark.asyncio
    async def test_task_manager_functionality(self):
        """测试任务管理器功能"""
        # 初始化任务管理器
        await task_manager.initialize()
        
        # 创建测试任务
        task_id = await task_manager.create_task(
            TaskType.PHOTO_RESTORE,
            {"test": "data"},
            user_id="test_user",
            priority=5
        )
        
        assert task_id is not None
        
        # 获取任务状态
        task = await task_manager.get_task(task_id)
        assert task is not None
        assert task.task_type == TaskType.PHOTO_RESTORE
        assert task.status == TaskStatus.PENDING
        
        # 更新任务进度
        await task_manager.update_task_progress(
            task_id, 1, 2, "测试进度更新"
        )
        
        updated_task = await task_manager.get_task(task_id)
        assert updated_task.progress.current_step == 1
        assert updated_task.progress.total_steps == 2
        assert updated_task.progress.percentage == 50.0

    @pytest.mark.asyncio
    async def test_batch_processor_optimization(self):
        """测试批量处理器优化功能"""
        replicate_service = Mock(spec=ReplicateService)
        batch_processor = BatchProcessor(replicate_service)
        
        # 测试文件路径
        test_files = [f"test_{i}.jpg" for i in range(10)]
        
        # 获取优化建议
        optimization = await batch_processor.optimize_batch_processing(
            test_files, "restore"
        )
        
        assert "total_files" in optimization
        assert "recommended_concurrent" in optimization
        assert "estimated_total_time_minutes" in optimization
        assert "optimization_tips" in optimization
        
        assert optimization["total_files"] == 10
        assert optimization["recommended_concurrent"] <= 3

    def test_voice_language_detection(self):
        """测试语言检测功能"""
        voice_cloner = MultiLanguageVoiceCloner(Mock())
        
        # 测试中文文本
        chinese_text = "这是一段中文文本"
        recommendations = voice_cloner.get_language_recommendations(chinese_text)
        
        assert len(recommendations) > 0
        assert recommendations[0]["language"] == "zh"
        assert recommendations[0]["confidence"] > 0.3
        
        # 测试英文文本
        english_text = "This is an English text"
        recommendations = voice_cloner.get_language_recommendations(english_text)
        
        # 应该推荐英文或默认语言
        assert len(recommendations) > 0

    @pytest.mark.asyncio
    async def test_error_handling(self, client, mock_user_token):
        """测试错误处理机制"""
        headers = {"Authorization": f"Bearer {mock_user_token}"}
        
        # 测试无效操作类型
        response = client.post(
            "/api/v1/ai-enhanced/batch/photo-process",
            files=[],
            data={"operation": "invalid_operation"},
            headers=headers
        )
        assert response.status_code == 400
        
        # 测试空文件列表
        response = client.post(
            "/api/v1/ai-enhanced/batch/photo-process",
            files=[],
            data={"operation": "restore"},
            headers=headers
        )
        assert response.status_code == 400

    @pytest.mark.asyncio
    async def test_task_cancellation(self):
        """测试任务取消功能"""
        await task_manager.initialize()
        
        # 创建任务
        task_id = await task_manager.create_task(
            TaskType.VOICE_CLONE,
            {"test": "data"},
            user_id="test_user"
        )
        
        # 取消任务
        await task_manager.cancel_task(task_id)
        
        # 验证任务状态
        task = await task_manager.get_task(task_id)
        assert task.status == TaskStatus.CANCELLED

    def test_performance_metrics(self, client):
        """测试性能指标"""
        import time
        
        # 测试API响应时间
        start_time = time.time()
        response = client.get("/api/v1/ai-enhanced/health")
        end_time = time.time()
        
        response_time = end_time - start_time
        assert response_time < 1.0  # 响应时间应小于1秒
        assert response.status_code == 200

    @pytest.mark.asyncio
    async def test_concurrent_task_processing(self):
        """测试并发任务处理"""
        await task_manager.initialize()
        
        # 创建多个任务
        tasks = []
        for i in range(5):
            task_id = await task_manager.create_task(
                TaskType.PHOTO_ENHANCE,
                {"file": f"test_{i}.jpg"},
                user_id=f"user_{i}",
                priority=i + 1
            )
            tasks.append(task_id)
        
        # 验证所有任务都被创建
        assert len(tasks) == 5
        
        # 验证任务优先级排序
        user_tasks = await task_manager.get_user_tasks("user_4")
        assert len(user_tasks) == 1

    def teardown_method(self, method):
        """清理测试数据"""
        # 清理临时文件
        temp_files = Path("/tmp").glob("test_*.wav")
        for file in temp_files:
            try:
                file.unlink()
            except FileNotFoundError:
                pass


class TestPerformance:
    """性能测试类"""
    
    @pytest.mark.asyncio
    async def test_memory_usage(self):
        """测试内存使用情况"""
        import psutil
        import gc
        
        process = psutil.Process()
        initial_memory = process.memory_info().rss
        
        # 执行一些操作
        await task_manager.initialize()
        
        # 强制垃圾回收
        gc.collect()
        
        final_memory = process.memory_info().rss
        memory_increase = final_memory - initial_memory
        
        # 内存增长应该在合理范围内（小于100MB）
        assert memory_increase < 100 * 1024 * 1024

    @pytest.mark.asyncio
    async def test_task_processing_speed(self):
        """测试任务处理速度"""
        await task_manager.initialize()
        
        start_time = time.time()
        
        # 创建100个任务
        for i in range(100):
            await task_manager.create_task(
                TaskType.PHOTO_RESTORE,
                {"test": f"data_{i}"},
                user_id="speed_test_user"
            )
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        # 100个任务创建应该在5秒内完成
        assert processing_time < 5.0
        
        # 验证任务数量
        user_tasks = await task_manager.get_user_tasks("speed_test_user")
        assert len(user_tasks) == 100


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v", "--tb=short"])
