"""
性能监控和质量保证工具
实时监控系统性能、API响应时间、错误率等关键指标
"""

import time
import asyncio
import logging
import psutil
import statistics
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from collections import defaultdict, deque
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

# 可选的 Redis 依赖
try:
    import redis.asyncio as aioredis
    REDIS_AVAILABLE = True
except ImportError:
    aioredis = None
    REDIS_AVAILABLE = False

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    timestamp: datetime
    response_time: float
    status_code: int
    endpoint: str
    method: str
    user_id: Optional[str] = None
    error_message: Optional[str] = None


@dataclass
class SystemMetrics:
    """系统指标数据类"""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    disk_usage_percent: float
    active_connections: int
    redis_memory_mb: Optional[float] = None


@dataclass
class QualityMetrics:
    """质量指标数据类"""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    avg_response_time: float = 0.0
    p95_response_time: float = 0.0
    p99_response_time: float = 0.0
    error_rate: float = 0.0
    uptime_seconds: int = 0
    active_tasks: int = 0
    completed_tasks: int = 0
    failed_tasks: int = 0


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, redis_url: Optional[str] = None):
        self.redis_url = redis_url
        self.redis_client = None
        self.start_time = datetime.now()

        # 内存中的指标存储（最近1小时）
        self.performance_metrics: deque = deque(maxlen=3600)  # 1小时，每秒1个
        self.system_metrics: deque = deque(maxlen=360)  # 1小时，每10秒1个

        # 实时统计
        self.endpoint_stats: Dict[str, List[float]] = defaultdict(list)
        self.error_counts: Dict[str, int] = defaultdict(int)
        self.active_requests: int = 0

        # 监控任务
        self._monitoring_task: Optional[asyncio.Task] = None
        self._is_monitoring = False

    async def initialize(self):
        """初始化监控器"""
        if not REDIS_AVAILABLE:
            logger.info("aioredis 未安装，使用内存监控模式")
            self.redis_client = None
        else:
            try:
                if self.redis_url:
                    self.redis_client = aioredis.from_url(self.redis_url)
                    await self.redis_client.ping()
                    logger.info("Redis连接成功，启用持久化监控")
                else:
                    logger.info("使用内存监控模式")

            except Exception as e:
                logger.warning(f"Redis连接失败，使用内存监控: {e}")
                self.redis_client = None

        # 启动系统监控任务
        await self.start_monitoring()

    async def start_monitoring(self):
        """启动监控任务"""
        if not self._is_monitoring:
            self._is_monitoring = True
            self._monitoring_task = asyncio.create_task(self._system_monitoring_loop())
            logger.info("性能监控已启动")

    async def stop_monitoring(self):
        """停止监控任务"""
        self._is_monitoring = False
        if self._monitoring_task:
            self._monitoring_task.cancel()
            try:
                await self._monitoring_task
            except asyncio.CancelledError:
                pass
        logger.info("性能监控已停止")

    async def record_request(self, metrics: PerformanceMetrics):
        """记录请求指标"""
        # 添加到内存队列
        self.performance_metrics.append(metrics)
        
        # 更新端点统计
        endpoint_key = f"{metrics.method}:{metrics.endpoint}"
        self.endpoint_stats[endpoint_key].append(metrics.response_time)
        
        # 限制每个端点的历史记录数量
        if len(self.endpoint_stats[endpoint_key]) > 1000:
            self.endpoint_stats[endpoint_key] = self.endpoint_stats[endpoint_key][-500:]
        
        # 记录错误
        if metrics.status_code >= 400:
            self.error_counts[endpoint_key] += 1
        
        # 持久化到Redis
        if self.redis_client:
            try:
                await self._persist_performance_metrics(metrics)
            except Exception as e:
                logger.warning(f"持久化性能指标失败: {e}")

    async def record_system_metrics(self):
        """记录系统指标"""
        try:
            # 获取系统指标
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # Redis内存使用（如果可用）
            redis_memory = None
            if self.redis_client:
                try:
                    info = await self.redis_client.info('memory')
                    redis_memory = info.get('used_memory', 0) / 1024 / 1024  # MB
                except Exception:
                    pass
            
            metrics = SystemMetrics(
                timestamp=datetime.now(),
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                memory_used_mb=memory.used / 1024 / 1024,
                disk_usage_percent=disk.percent,
                active_connections=self.active_requests,
                redis_memory_mb=redis_memory
            )
            
            # 添加到内存队列
            self.system_metrics.append(metrics)
            
            # 持久化到Redis
            if self.redis_client:
                try:
                    await self._persist_system_metrics(metrics)
                except Exception as e:
                    logger.warning(f"持久化系统指标失败: {e}")
                    
        except Exception as e:
            logger.error(f"记录系统指标失败: {e}")

    async def get_quality_metrics(self) -> QualityMetrics:
        """获取质量指标"""
        now = datetime.now()
        uptime = int((now - self.start_time).total_seconds())
        
        # 计算请求统计
        total_requests = len(self.performance_metrics)
        successful_requests = sum(1 for m in self.performance_metrics if m.status_code < 400)
        failed_requests = total_requests - successful_requests
        
        # 计算响应时间统计
        response_times = [m.response_time for m in self.performance_metrics]
        avg_response_time = statistics.mean(response_times) if response_times else 0.0
        
        p95_response_time = 0.0
        p99_response_time = 0.0
        if response_times:
            sorted_times = sorted(response_times)
            p95_index = int(len(sorted_times) * 0.95)
            p99_index = int(len(sorted_times) * 0.99)
            p95_response_time = sorted_times[p95_index] if p95_index < len(sorted_times) else sorted_times[-1]
            p99_response_time = sorted_times[p99_index] if p99_index < len(sorted_times) else sorted_times[-1]
        
        # 计算错误率
        error_rate = (failed_requests / total_requests * 100) if total_requests > 0 else 0.0
        
        # 获取任务统计（需要从任务管理器获取）
        active_tasks = 0
        completed_tasks = 0
        failed_tasks = 0
        
        try:
            from app.ai_services.ai_task_manager import task_manager
            if hasattr(task_manager, 'get_task_statistics'):
                task_stats = await task_manager.get_task_statistics()
                active_tasks = task_stats.get('active', 0)
                completed_tasks = task_stats.get('completed', 0)
                failed_tasks = task_stats.get('failed', 0)
        except Exception as e:
            logger.warning(f"获取任务统计失败: {e}")
        
        return QualityMetrics(
            total_requests=total_requests,
            successful_requests=successful_requests,
            failed_requests=failed_requests,
            avg_response_time=avg_response_time,
            p95_response_time=p95_response_time,
            p99_response_time=p99_response_time,
            error_rate=error_rate,
            uptime_seconds=uptime,
            active_tasks=active_tasks,
            completed_tasks=completed_tasks,
            failed_tasks=failed_tasks
        )

    async def get_endpoint_statistics(self) -> Dict[str, Dict[str, Any]]:
        """获取端点统计信息"""
        stats = {}
        
        for endpoint, response_times in self.endpoint_stats.items():
            if response_times:
                stats[endpoint] = {
                    'total_requests': len(response_times),
                    'avg_response_time': statistics.mean(response_times),
                    'min_response_time': min(response_times),
                    'max_response_time': max(response_times),
                    'error_count': self.error_counts.get(endpoint, 0),
                    'error_rate': (self.error_counts.get(endpoint, 0) / len(response_times)) * 100
                }
        
        return stats

    async def get_system_health(self) -> Dict[str, Any]:
        """获取系统健康状态"""
        if not self.system_metrics:
            return {"status": "no_data"}
        
        latest_metrics = self.system_metrics[-1]
        
        # 健康状态判断
        health_status = "healthy"
        warnings = []
        
        if latest_metrics.cpu_percent > 80:
            health_status = "warning"
            warnings.append(f"CPU使用率过高: {latest_metrics.cpu_percent:.1f}%")
        
        if latest_metrics.memory_percent > 85:
            health_status = "warning"
            warnings.append(f"内存使用率过高: {latest_metrics.memory_percent:.1f}%")
        
        if latest_metrics.disk_usage_percent > 90:
            health_status = "critical"
            warnings.append(f"磁盘使用率过高: {latest_metrics.disk_usage_percent:.1f}%")
        
        return {
            "status": health_status,
            "warnings": warnings,
            "metrics": {
                "cpu_percent": latest_metrics.cpu_percent,
                "memory_percent": latest_metrics.memory_percent,
                "memory_used_mb": latest_metrics.memory_used_mb,
                "disk_usage_percent": latest_metrics.disk_usage_percent,
                "active_connections": latest_metrics.active_connections,
                "redis_memory_mb": latest_metrics.redis_memory_mb
            },
            "timestamp": latest_metrics.timestamp.isoformat()
        }

    async def get_performance_trends(self, hours: int = 1) -> Dict[str, List[Dict[str, Any]]]:
        """获取性能趋势数据"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        # 过滤指定时间范围内的数据
        recent_performance = [m for m in self.performance_metrics if m.timestamp >= cutoff_time]
        recent_system = [m for m in self.system_metrics if m.timestamp >= cutoff_time]
        
        # 按时间分组（每5分钟一个数据点）
        performance_trends = []
        system_trends = []
        
        # 简化实现：每10个数据点取一个平均值
        if recent_performance:
            chunk_size = max(1, len(recent_performance) // 20)  # 最多20个数据点
            for i in range(0, len(recent_performance), chunk_size):
                chunk = recent_performance[i:i + chunk_size]
                avg_response_time = statistics.mean([m.response_time for m in chunk])
                error_rate = sum(1 for m in chunk if m.status_code >= 400) / len(chunk) * 100
                
                performance_trends.append({
                    "timestamp": chunk[0].timestamp.isoformat(),
                    "avg_response_time": avg_response_time,
                    "error_rate": error_rate,
                    "request_count": len(chunk)
                })
        
        if recent_system:
            chunk_size = max(1, len(recent_system) // 20)
            for i in range(0, len(recent_system), chunk_size):
                chunk = recent_system[i:i + chunk_size]
                avg_cpu = statistics.mean([m.cpu_percent for m in chunk])
                avg_memory = statistics.mean([m.memory_percent for m in chunk])
                
                system_trends.append({
                    "timestamp": chunk[0].timestamp.isoformat(),
                    "cpu_percent": avg_cpu,
                    "memory_percent": avg_memory
                })
        
        return {
            "performance": performance_trends,
            "system": system_trends
        }

    async def _system_monitoring_loop(self):
        """系统监控循环"""
        while self._is_monitoring:
            try:
                await self.record_system_metrics()
                await asyncio.sleep(10)  # 每10秒记录一次系统指标
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"系统监控循环错误: {e}")
                await asyncio.sleep(10)

    async def _persist_performance_metrics(self, metrics: PerformanceMetrics):
        """持久化性能指标到Redis"""
        if not self.redis_client:
            return
        
        key = f"performance_metrics:{metrics.timestamp.strftime('%Y%m%d%H')}"
        data = {
            "timestamp": metrics.timestamp.isoformat(),
            "response_time": metrics.response_time,
            "status_code": metrics.status_code,
            "endpoint": metrics.endpoint,
            "method": metrics.method,
            "user_id": metrics.user_id,
            "error_message": metrics.error_message
        }
        
        await self.redis_client.lpush(key, str(data))
        await self.redis_client.expire(key, 86400 * 7)  # 保留7天

    async def _persist_system_metrics(self, metrics: SystemMetrics):
        """持久化系统指标到Redis"""
        if not self.redis_client:
            return
        
        key = f"system_metrics:{metrics.timestamp.strftime('%Y%m%d%H')}"
        data = {
            "timestamp": metrics.timestamp.isoformat(),
            "cpu_percent": metrics.cpu_percent,
            "memory_percent": metrics.memory_percent,
            "memory_used_mb": metrics.memory_used_mb,
            "disk_usage_percent": metrics.disk_usage_percent,
            "active_connections": metrics.active_connections,
            "redis_memory_mb": metrics.redis_memory_mb
        }
        
        await self.redis_client.lpush(key, str(data))
        await self.redis_client.expire(key, 86400 * 7)  # 保留7天

    async def cleanup(self):
        """清理资源"""
        await self.stop_monitoring()
        if self.redis_client:
            await self.redis_client.close()


class PerformanceMiddleware(BaseHTTPMiddleware):
    """性能监控中间件"""
    
    def __init__(self, app, monitor: PerformanceMonitor):
        super().__init__(app)
        self.monitor = monitor

    async def dispatch(self, request: Request, call_next):
        start_time = time.time()
        self.monitor.active_requests += 1
        
        try:
            response = await call_next(request)
            
            # 记录性能指标
            end_time = time.time()
            response_time = end_time - start_time
            
            # 获取用户ID（如果有认证信息）
            user_id = None
            if hasattr(request.state, 'user_id'):
                user_id = request.state.user_id
            
            metrics = PerformanceMetrics(
                timestamp=datetime.now(),
                response_time=response_time,
                status_code=response.status_code,
                endpoint=request.url.path,
                method=request.method,
                user_id=user_id
            )
            
            await self.monitor.record_request(metrics)
            
            return response
            
        except Exception as e:
            # 记录错误指标
            end_time = time.time()
            response_time = end_time - start_time
            
            metrics = PerformanceMetrics(
                timestamp=datetime.now(),
                response_time=response_time,
                status_code=500,
                endpoint=request.url.path,
                method=request.method,
                error_message=str(e)
            )
            
            await self.monitor.record_request(metrics)
            raise
            
        finally:
            self.monitor.active_requests -= 1


# 全局监控器实例
performance_monitor = PerformanceMonitor()
