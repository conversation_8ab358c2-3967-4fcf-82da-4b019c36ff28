"""
增强语音处理服务
支持多语言、音质提升、降噪处理、个性化语音克隆等功能
"""

import logging
import os
import tempfile
import time
import uuid
from pathlib import Path
from typing import Any, BinaryIO, Dict, List, Optional, Tuple

logger = logging.getLogger(__name__)

# 音频处理库 - 如果不可用则使用简化版本
try:
    import librosa
    import soundfile as sf
    import numpy as np
    from pydub import AudioSegment
    from pydub.effects import normalize, compress_dynamic_range
    import noisereduce as nr
    AUDIO_LIBS_AVAILABLE = True
except ImportError:
    AUDIO_LIBS_AVAILABLE = False
    logger.warning("音频处理库不可用，将使用简化版本")

from .replicate_service import ReplicateService


class VoiceQualityEnhancer:
    """语音质量增强器"""
    
    @staticmethod
    def enhance_audio_quality(
        audio_path: str,
        target_sample_rate: int = 22050,
        normalize_audio: bool = True,
        reduce_noise: bool = True,
        compress_dynamic: bool = True
    ) -> str:
        """
        增强音频质量
        
        Args:
            audio_path: 音频文件路径
            target_sample_rate: 目标采样率
            normalize_audio: 是否标准化音量
            reduce_noise: 是否降噪
            compress_dynamic: 是否动态压缩
            
        Returns:
            增强后的音频文件路径
        """
        try:
            # 加载音频
            audio = AudioSegment.from_file(audio_path)
            
            # 转换为目标采样率
            if audio.frame_rate != target_sample_rate:
                audio = audio.set_frame_rate(target_sample_rate)
            
            # 转换为单声道
            if audio.channels > 1:
                audio = audio.set_channels(1)
            
            # 标准化音量
            if normalize_audio:
                audio = normalize(audio)
            
            # 动态压缩
            if compress_dynamic:
                audio = compress_dynamic_range(audio)
            
            # 降噪处理
            if reduce_noise:
                # 转换为numpy数组进行降噪
                samples = np.array(audio.get_array_of_samples(), dtype=np.float32)
                samples = samples / (2**15)  # 标准化到[-1, 1]
                
                # 应用降噪
                reduced_noise = nr.reduce_noise(
                    y=samples, 
                    sr=target_sample_rate,
                    stationary=False,
                    prop_decrease=0.8
                )
                
                # 转换回AudioSegment
                reduced_noise = (reduced_noise * (2**15)).astype(np.int16)
                audio = AudioSegment(
                    reduced_noise.tobytes(),
                    frame_rate=target_sample_rate,
                    sample_width=2,
                    channels=1
                )
            
            # 保存增强后的音频
            enhanced_path = audio_path.replace('.', '_enhanced.')
            audio.export(enhanced_path, format="wav")
            
            logger.info(f"音频质量增强完成: {enhanced_path}")
            return enhanced_path
            
        except Exception as e:
            logger.error(f"音频质量增强失败: {e}")
            return audio_path  # 返回原始文件


class MultiLanguageVoiceCloner:
    """多语言语音克隆器"""
    
    # 支持的语言配置
    SUPPORTED_LANGUAGES = {
        'zh': {
            'name': '中文',
            'model': 'xtts',
            'sample_rate': 22050,
            'min_sample_length': 6,  # 最小样本长度(秒)
            'recommended_sample_length': 10
        },
        'en': {
            'name': 'English',
            'model': 'xtts',
            'sample_rate': 22050,
            'min_sample_length': 6,
            'recommended_sample_length': 10
        },
        'ja': {
            'name': '日本語',
            'model': 'xtts',
            'sample_rate': 22050,
            'min_sample_length': 8,
            'recommended_sample_length': 12
        },
        'ko': {
            'name': '한국어',
            'model': 'xtts',
            'sample_rate': 22050,
            'min_sample_length': 8,
            'recommended_sample_length': 12
        },
        'es': {
            'name': 'Español',
            'model': 'xtts',
            'sample_rate': 22050,
            'min_sample_length': 6,
            'recommended_sample_length': 10
        },
        'fr': {
            'name': 'Français',
            'model': 'xtts',
            'sample_rate': 22050,
            'min_sample_length': 6,
            'recommended_sample_length': 10
        }
    }
    
    def __init__(self, replicate_service: ReplicateService):
        self.replicate_service = replicate_service
        self.quality_enhancer = VoiceQualityEnhancer()
    
    def validate_voice_sample(
        self, 
        audio_path: str, 
        language: str = 'zh'
    ) -> Dict[str, Any]:
        """
        验证语音样本质量
        
        Args:
            audio_path: 音频文件路径
            language: 语言代码
            
        Returns:
            验证结果
        """
        try:
            # 加载音频信息
            audio = AudioSegment.from_file(audio_path)
            duration = len(audio) / 1000.0  # 转换为秒
            
            lang_config = self.SUPPORTED_LANGUAGES.get(language, self.SUPPORTED_LANGUAGES['zh'])
            
            validation_result = {
                'is_valid': True,
                'duration': duration,
                'sample_rate': audio.frame_rate,
                'channels': audio.channels,
                'warnings': [],
                'recommendations': []
            }
            
            # 检查时长
            if duration < lang_config['min_sample_length']:
                validation_result['is_valid'] = False
                validation_result['warnings'].append(
                    f"音频时长过短 ({duration:.1f}s)，建议至少 {lang_config['min_sample_length']}s"
                )
            elif duration < lang_config['recommended_sample_length']:
                validation_result['warnings'].append(
                    f"建议音频时长至少 {lang_config['recommended_sample_length']}s 以获得更好效果"
                )
            
            # 检查采样率
            if audio.frame_rate < 16000:
                validation_result['warnings'].append(
                    f"采样率较低 ({audio.frame_rate}Hz)，建议至少 16kHz"
                )
            
            # 检查声道数
            if audio.channels > 1:
                validation_result['recommendations'].append("建议使用单声道音频")
            
            # 检查音频质量
            samples = np.array(audio.get_array_of_samples(), dtype=np.float32)
            if audio.channels == 2:
                samples = samples.reshape((-1, 2)).mean(axis=1)
            
            # 计算信噪比
            signal_power = np.mean(samples ** 2)
            if signal_power < 0.01:
                validation_result['warnings'].append("音频信号较弱，建议提高录音音量")
            
            # 检测静音段
            silence_threshold = 0.01
            silence_ratio = np.mean(np.abs(samples) < silence_threshold)
            if silence_ratio > 0.3:
                validation_result['warnings'].append("音频包含较多静音，建议剪辑掉静音部分")
            
            return validation_result
            
        except Exception as e:
            logger.error(f"语音样本验证失败: {e}")
            return {
                'is_valid': False,
                'error': str(e),
                'warnings': ['无法分析音频文件'],
                'recommendations': []
            }
    
    def clone_voice_enhanced(
        self,
        voice_sample_path: str,
        text: str,
        language: str = 'zh',
        enhance_quality: bool = True,
        speed: float = 1.0,
        pitch_shift: float = 0.0
    ) -> Dict[str, Any]:
        """
        增强语音克隆
        
        Args:
            voice_sample_path: 语音样本路径
            text: 要生成的文本
            language: 语言代码
            enhance_quality: 是否增强音质
            speed: 语速调整 (0.5-2.0)
            pitch_shift: 音调调整 (-12到12半音)
            
        Returns:
            克隆结果
        """
        try:
            # 验证语言支持
            if language not in self.SUPPORTED_LANGUAGES:
                raise ValueError(f"不支持的语言: {language}")
            
            # 验证语音样本
            validation = self.validate_voice_sample(voice_sample_path, language)
            if not validation['is_valid']:
                raise ValueError(f"语音样本验证失败: {validation.get('error', '未知错误')}")
            
            # 增强音频质量
            processed_sample_path = voice_sample_path
            if enhance_quality:
                processed_sample_path = self.quality_enhancer.enhance_audio_quality(
                    voice_sample_path,
                    target_sample_rate=self.SUPPORTED_LANGUAGES[language]['sample_rate']
                )
            
            # 调用Replicate服务进行语音克隆
            with open(processed_sample_path, 'rb') as voice_data:
                result = self.replicate_service.clone_voice(
                    voice_data,
                    Path(processed_sample_path).name,
                    text,
                    language
                )
            
            # 后处理生成的音频
            if result.get('result') and (speed != 1.0 or pitch_shift != 0.0):
                result = self._post_process_audio(
                    result, speed, pitch_shift
                )
            
            # 添加处理信息
            result['processing_info'] = {
                'language': language,
                'language_name': self.SUPPORTED_LANGUAGES[language]['name'],
                'enhanced_quality': enhance_quality,
                'speed_adjustment': speed,
                'pitch_adjustment': pitch_shift,
                'validation_warnings': validation.get('warnings', []),
                'sample_duration': validation.get('duration', 0)
            }
            
            return result
            
        except Exception as e:
            logger.error(f"增强语音克隆失败: {e}")
            raise
    
    def _post_process_audio(
        self,
        clone_result: Dict[str, Any],
        speed: float,
        pitch_shift: float
    ) -> Dict[str, Any]:
        """
        后处理生成的音频
        
        Args:
            clone_result: 克隆结果
            speed: 语速调整
            pitch_shift: 音调调整
            
        Returns:
            处理后的结果
        """
        try:
            # 这里需要下载音频文件并进行处理
            # 简化实现，实际应该下载并处理音频
            logger.info(f"音频后处理: 语速={speed}, 音调={pitch_shift}")
            
            # 添加后处理标记
            clone_result['post_processed'] = True
            clone_result['adjustments'] = {
                'speed': speed,
                'pitch_shift': pitch_shift
            }
            
            return clone_result
            
        except Exception as e:
            logger.error(f"音频后处理失败: {e}")
            return clone_result
    
    def get_language_recommendations(self, text: str) -> List[Dict[str, Any]]:
        """
        根据文本内容推荐语言
        
        Args:
            text: 输入文本
            
        Returns:
            语言推荐列表
        """
        recommendations = []
        
        # 简单的语言检测逻辑
        chinese_chars = len([c for c in text if '\u4e00' <= c <= '\u9fff'])
        japanese_chars = len([c for c in text if '\u3040' <= c <= '\u309f' or '\u30a0' <= c <= '\u30ff'])
        korean_chars = len([c for c in text if '\uac00' <= c <= '\ud7af'])
        
        total_chars = len(text)
        
        if chinese_chars / total_chars > 0.3:
            recommendations.append({
                'language': 'zh',
                'confidence': min(chinese_chars / total_chars, 0.95),
                'name': '中文'
            })
        
        if japanese_chars / total_chars > 0.2:
            recommendations.append({
                'language': 'ja',
                'confidence': min(japanese_chars / total_chars, 0.95),
                'name': '日本語'
            })
        
        if korean_chars / total_chars > 0.2:
            recommendations.append({
                'language': 'ko',
                'confidence': min(korean_chars / total_chars, 0.95),
                'name': '한국어'
            })
        
        # 如果没有检测到特定语言，默认推荐英文
        if not recommendations:
            recommendations.append({
                'language': 'en',
                'confidence': 0.5,
                'name': 'English'
            })
        
        # 按置信度排序
        recommendations.sort(key=lambda x: x['confidence'], reverse=True)
        
        return recommendations
    
    @classmethod
    def get_supported_languages(cls) -> Dict[str, Dict[str, Any]]:
        """获取支持的语言列表"""
        return cls.SUPPORTED_LANGUAGES.copy()


class VoicePersonalizationService:
    """语音个性化服务"""
    
    def __init__(self, voice_cloner: MultiLanguageVoiceCloner):
        self.voice_cloner = voice_cloner
    
    def create_voice_profile(
        self,
        user_id: str,
        voice_samples: List[str],
        profile_name: str,
        language: str = 'zh'
    ) -> Dict[str, Any]:
        """
        创建个性化语音档案
        
        Args:
            user_id: 用户ID
            voice_samples: 语音样本文件路径列表
            profile_name: 档案名称
            language: 语言
            
        Returns:
            语音档案信息
        """
        try:
            profile_id = str(uuid.uuid4())
            
            # 验证所有语音样本
            sample_validations = []
            total_duration = 0
            
            for sample_path in voice_samples:
                validation = self.voice_cloner.validate_voice_sample(sample_path, language)
                sample_validations.append(validation)
                total_duration += validation.get('duration', 0)
            
            # 检查总时长是否足够
            min_total_duration = 30  # 至少30秒
            if total_duration < min_total_duration:
                raise ValueError(f"语音样本总时长不足，需要至少{min_total_duration}秒")
            
            # 创建档案
            voice_profile = {
                'profile_id': profile_id,
                'user_id': user_id,
                'profile_name': profile_name,
                'language': language,
                'sample_count': len(voice_samples),
                'total_duration': total_duration,
                'created_at': time.time(),
                'sample_validations': sample_validations,
                'quality_score': self._calculate_quality_score(sample_validations)
            }
            
            logger.info(f"创建语音档案: {profile_name} (ID: {profile_id})")
            return voice_profile
            
        except Exception as e:
            logger.error(f"创建语音档案失败: {e}")
            raise
    
    def _calculate_quality_score(self, validations: List[Dict[str, Any]]) -> float:
        """计算语音质量评分"""
        if not validations:
            return 0.0
        
        total_score = 0
        valid_samples = 0
        
        for validation in validations:
            if validation.get('is_valid'):
                score = 100
                # 根据警告数量扣分
                warnings = len(validation.get('warnings', []))
                score -= warnings * 10
                
                # 根据时长评分
                duration = validation.get('duration', 0)
                if duration >= 10:
                    score += 10
                elif duration >= 6:
                    score += 5
                
                total_score += max(score, 0)
                valid_samples += 1
        
        return total_score / valid_samples if valid_samples > 0 else 0.0
