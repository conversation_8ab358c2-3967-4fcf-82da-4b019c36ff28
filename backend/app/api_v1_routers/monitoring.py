"""
监控仪表板API路由
提供系统性能、质量指标、健康状态等监控数据
"""

import logging
from typing import Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, status
from fastapi.responses import JSONResponse

from app.monitoring.performance_monitor import performance_monitor
from app.api_v1_routers.deps import get_current_active_user
from app.models_sqlalchemy import User

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/health", summary="系统健康检查")
async def get_system_health() -> JSONResponse:
    """
    获取系统健康状态
    包括CPU、内存、磁盘使用率等关键指标
    """
    try:
        health_data = await performance_monitor.get_system_health()
        
        # 根据健康状态设置HTTP状态码
        status_code = status.HTTP_200_OK
        if health_data.get("status") == "warning":
            status_code = status.HTTP_200_OK  # 警告仍返回200
        elif health_data.get("status") == "critical":
            status_code = status.HTTP_503_SERVICE_UNAVAILABLE
        
        return JSONResponse(
            status_code=status_code,
            content={
                "success": True,
                "data": health_data,
                "timestamp": health_data.get("timestamp")
            }
        )
        
    except Exception as e:
        logger.error(f"获取系统健康状态失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取系统健康状态失败: {str(e)}"
        )


@router.get("/metrics/quality", summary="质量指标")
async def get_quality_metrics(
    current_user: User = Depends(get_current_active_user)
) -> JSONResponse:
    """
    获取系统质量指标
    包括请求统计、响应时间、错误率、任务统计等
    """
    try:
        quality_metrics = await performance_monitor.get_quality_metrics()
        
        return JSONResponse(content={
            "success": True,
            "data": {
                "requests": {
                    "total": quality_metrics.total_requests,
                    "successful": quality_metrics.successful_requests,
                    "failed": quality_metrics.failed_requests,
                    "success_rate": (
                        (quality_metrics.successful_requests / quality_metrics.total_requests * 100)
                        if quality_metrics.total_requests > 0 else 100.0
                    )
                },
                "performance": {
                    "avg_response_time_ms": round(quality_metrics.avg_response_time * 1000, 2),
                    "p95_response_time_ms": round(quality_metrics.p95_response_time * 1000, 2),
                    "p99_response_time_ms": round(quality_metrics.p99_response_time * 1000, 2),
                    "error_rate_percent": round(quality_metrics.error_rate, 2)
                },
                "system": {
                    "uptime_seconds": quality_metrics.uptime_seconds,
                    "uptime_hours": round(quality_metrics.uptime_seconds / 3600, 2)
                },
                "tasks": {
                    "active": quality_metrics.active_tasks,
                    "completed": quality_metrics.completed_tasks,
                    "failed": quality_metrics.failed_tasks,
                    "total": (
                        quality_metrics.active_tasks + 
                        quality_metrics.completed_tasks + 
                        quality_metrics.failed_tasks
                    )
                }
            }
        })
        
    except Exception as e:
        logger.error(f"获取质量指标失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取质量指标失败: {str(e)}"
        )


@router.get("/metrics/endpoints", summary="端点统计")
async def get_endpoint_statistics(
    current_user: User = Depends(get_current_active_user)
) -> JSONResponse:
    """
    获取API端点统计信息
    包括各端点的请求量、响应时间、错误率等
    """
    try:
        endpoint_stats = await performance_monitor.get_endpoint_statistics()
        
        # 按请求量排序
        sorted_endpoints = sorted(
            endpoint_stats.items(),
            key=lambda x: x[1]['total_requests'],
            reverse=True
        )
        
        return JSONResponse(content={
            "success": True,
            "data": {
                "endpoints": dict(sorted_endpoints),
                "summary": {
                    "total_endpoints": len(endpoint_stats),
                    "most_used_endpoint": sorted_endpoints[0][0] if sorted_endpoints else None,
                    "highest_error_rate": max(
                        (stats['error_rate'] for stats in endpoint_stats.values()),
                        default=0.0
                    )
                }
            }
        })
        
    except Exception as e:
        logger.error(f"获取端点统计失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取端点统计失败: {str(e)}"
        )


@router.get("/metrics/trends", summary="性能趋势")
async def get_performance_trends(
    hours: int = Query(1, ge=1, le=24, description="时间范围（小时）"),
    current_user: User = Depends(get_current_active_user)
) -> JSONResponse:
    """
    获取性能趋势数据
    包括响应时间、错误率、系统资源使用率的历史趋势
    """
    try:
        trends_data = await performance_monitor.get_performance_trends(hours)
        
        return JSONResponse(content={
            "success": True,
            "data": {
                "time_range_hours": hours,
                "performance_trends": trends_data["performance"],
                "system_trends": trends_data["system"],
                "data_points": {
                    "performance": len(trends_data["performance"]),
                    "system": len(trends_data["system"])
                }
            }
        })
        
    except Exception as e:
        logger.error(f"获取性能趋势失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取性能趋势失败: {str(e)}"
        )


@router.get("/dashboard", summary="监控仪表板")
async def get_monitoring_dashboard(
    current_user: User = Depends(get_current_active_user)
) -> JSONResponse:
    """
    获取完整的监控仪表板数据
    包括系统健康、质量指标、端点统计等综合信息
    """
    try:
        # 并行获取所有监控数据
        import asyncio
        
        health_task = performance_monitor.get_system_health()
        quality_task = performance_monitor.get_quality_metrics()
        endpoints_task = performance_monitor.get_endpoint_statistics()
        trends_task = performance_monitor.get_performance_trends(1)
        
        health_data, quality_metrics, endpoint_stats, trends_data = await asyncio.gather(
            health_task, quality_task, endpoints_task, trends_task
        )
        
        # 计算关键指标
        total_requests = quality_metrics.total_requests
        success_rate = (
            (quality_metrics.successful_requests / total_requests * 100)
            if total_requests > 0 else 100.0
        )
        
        # 获取最活跃的端点
        most_active_endpoints = sorted(
            endpoint_stats.items(),
            key=lambda x: x[1]['total_requests'],
            reverse=True
        )[:5]  # 前5个最活跃的端点
        
        return JSONResponse(content={
            "success": True,
            "data": {
                "overview": {
                    "system_status": health_data.get("status", "unknown"),
                    "total_requests": total_requests,
                    "success_rate": round(success_rate, 2),
                    "avg_response_time_ms": round(quality_metrics.avg_response_time * 1000, 2),
                    "error_rate": round(quality_metrics.error_rate, 2),
                    "uptime_hours": round(quality_metrics.uptime_seconds / 3600, 2),
                    "active_tasks": quality_metrics.active_tasks
                },
                "system_health": health_data,
                "performance_summary": {
                    "response_times": {
                        "avg": round(quality_metrics.avg_response_time * 1000, 2),
                        "p95": round(quality_metrics.p95_response_time * 1000, 2),
                        "p99": round(quality_metrics.p99_response_time * 1000, 2)
                    },
                    "requests": {
                        "total": total_requests,
                        "successful": quality_metrics.successful_requests,
                        "failed": quality_metrics.failed_requests,
                        "success_rate": round(success_rate, 2)
                    }
                },
                "top_endpoints": [
                    {
                        "endpoint": endpoint,
                        "requests": stats["total_requests"],
                        "avg_response_time": round(stats["avg_response_time"] * 1000, 2),
                        "error_rate": round(stats["error_rate"], 2)
                    }
                    for endpoint, stats in most_active_endpoints
                ],
                "recent_trends": {
                    "performance": trends_data["performance"][-10:],  # 最近10个数据点
                    "system": trends_data["system"][-10:]
                },
                "alerts": health_data.get("warnings", [])
            }
        })
        
    except Exception as e:
        logger.error(f"获取监控仪表板失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取监控仪表板失败: {str(e)}"
        )


@router.post("/alerts/test", summary="测试告警")
async def test_alert_system(
    current_user: User = Depends(get_current_active_user)
) -> JSONResponse:
    """
    测试告警系统
    模拟各种告警场景以验证监控系统的响应
    """
    try:
        # 模拟告警测试
        test_results = {
            "cpu_alert": "模拟CPU使用率过高告警",
            "memory_alert": "模拟内存使用率过高告警",
            "error_rate_alert": "模拟错误率过高告警",
            "response_time_alert": "模拟响应时间过长告警"
        }
        
        logger.info("执行告警系统测试")
        
        return JSONResponse(content={
            "success": True,
            "message": "告警系统测试完成",
            "test_results": test_results
        })
        
    except Exception as e:
        logger.error(f"告警系统测试失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"告警系统测试失败: {str(e)}"
        )


@router.get("/status", summary="监控系统状态")
async def get_monitoring_status() -> JSONResponse:
    """
    获取监控系统自身的状态
    包括监控器运行状态、数据收集情况等
    """
    try:
        # 检查监控器状态
        is_monitoring = performance_monitor._is_monitoring
        metrics_count = len(performance_monitor.performance_metrics)
        system_metrics_count = len(performance_monitor.system_metrics)
        
        # 检查Redis连接状态
        redis_status = "disconnected"
        if performance_monitor.redis_client:
            try:
                await performance_monitor.redis_client.ping()
                redis_status = "connected"
            except Exception:
                redis_status = "error"
        
        return JSONResponse(content={
            "success": True,
            "data": {
                "monitoring_active": is_monitoring,
                "redis_status": redis_status,
                "metrics_collected": {
                    "performance_metrics": metrics_count,
                    "system_metrics": system_metrics_count
                },
                "data_retention": {
                    "memory_limit": "1小时",
                    "redis_retention": "7天" if redis_status == "connected" else "不可用"
                },
                "collection_intervals": {
                    "performance_metrics": "每个请求",
                    "system_metrics": "每10秒"
                }
            }
        })
        
    except Exception as e:
        logger.error(f"获取监控状态失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取监控状态失败: {str(e)}"
        )
